
import express from "express";
import {
    getTransactionsByUserId,
    createTransaction,
    deleteTransaction,
    putTransaction,
    sumaryTransactionsByUserId
} from "../controllers/transactionsController.js";
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All transaction routes require authentication
router.get('/summary/:userId', authenticateUser, sumaryTransactionsByUserId);
router.get('/:userId', authenticateUser, getTransactionsByUserId);
router.post('/', authenticateUser, createTransaction);
router.delete('/:id', authenticateUser, deleteTransaction);
router.put('/:id', authenticateUser, putTransaction);

export default router;