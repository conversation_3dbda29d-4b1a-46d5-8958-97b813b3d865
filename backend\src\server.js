import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import {initDB} from './config/db.js';
import rateLimiter from './middleware/rateLimiter.js';
import transactionsRoute from './routes/transactionsRoute.js';
import productsRoute from './routes/productsRoute.js';
import usersRoute from './routes/usersRoute.js';
import job from "./config/cron.js";


dotenv.config();

const app = express();

if (process.env.NODE_ENV === "production") job.start();

// middleware req ve server arasındaki bağlantı

app.use(cors());
app.use(express.json());
app.use(rateLimiter);


// our custom middleware
// app.use((req,res,next) =>  {
//     console.log('Request received',req.method);
//     next();
// });

const PORT = process.env.PORT || 5001;

app.get("/api/health",(req,res) => {
    res.status(200).json({status: "ok"});
});

// Routes
app.use('/api/transactions', transactionsRoute);
app.use('/api/products', productsRoute);
app.use('/api/users', usersRoute);

// TODO: Implement these routes
// app.use('/api/reviews', reviewsRoute);
// app.use('/api/cart', cartRoute);
// app.use('/api/orders', ordersRoute);
// app.use('/api/notifications', notificationsRoute);
// app.use('/api/reports', reportsRoute);
// app.use('/api/faqs', faqsRoute);
// app.use('/api/lotteries', lotteriesRoute);
// app.use('/api/auctions', auctionsRoute);
// app.use('/api/watchlist', watchlistRoute);
// app.use('/api/bids', bidsRoute);
// app.use('/api/tickets', ticketsRoute);
// app.use('/api/settings', settingsRoute);
// app.use('/api/analytics', analyticsRoute);


initDB().then(() => {
    app.listen(PORT, () => {
        console.log('Server is up and runnig on port:', PORT);
    });
});
