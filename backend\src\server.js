import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import {initDB} from './config/db.js';
import rateLimiter from './middleware/rateLimiter.js';
import transactionsRoute from './routes/transactionsRoute.js';
import job from "./config/cron.js";


dotenv.config();

const app = express();

if (process.env.NODE_ENV === "production") job.start();

// middleware req ve server arasındaki bağlantı

app.use(cors());
app.use(express.json());
app.use(rateLimiter);


// our custom middleware
// app.use((req,res,next) =>  {
//     console.log('Request received',req.method);
//     next();
// });

const PORT = process.env.PORT || 5001;

app.get("/api/health",(req,res) => {
    res.status(200).json({status: "ok"});
});



app.use('/api/transactions', transactionsRoute);
app.use('/api/products', transactionsRoute);
app.use('/api/users', transactionsRoute);
app.use('/api/reviews', transactionsRoute);
app.use('/api/cart', transactionsRoute);
app.use('/api/orders', transactionsRoute);
app.use('/api/notifications', transactionsRoute);
app.use('/api/reports', transactionsRoute);
app.use('/api/faqs', transactionsRoute);
app.use('/api/lotteries', transactionsRoute);
app.use('/api/auctions', transactionsRoute);
app.use('/api/watchlist', transactionsRoute);
app.use('/api/bids', transactionsRoute);
app.use('/api/tickets', transactionsRoute);
app.use('/api/settings', transactionsRoute);
app.use('/api/analytics', transactionsRoute);


initDB().then(() => {
    app.listen(PORT, () => {
        console.log('Server is up and runnig on port:', PORT);
    });
});
