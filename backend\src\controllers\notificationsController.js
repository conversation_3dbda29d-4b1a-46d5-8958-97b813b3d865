import { sql } from "../config/db.js";

// Get user notifications
export async function getUserNotifications(req, res) {
    try {
        const userId = req.userId;
        const { 
            page = 1, 
            limit = 20, 
            type,
            read_status,
            sortBy = 'created_at',
            sortOrder = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        
        let whereConditions = [`user_id = '${userId}'`];
        if (type) whereConditions.push(`type = '${type}'`);
        if (read_status !== undefined) whereConditions.push(`is_read = ${read_status === 'true'}`);
        
        const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

        const notifications = await sql`
            SELECT * FROM notifications
            ${sql.unsafe(whereClause)}
            ORDER BY ${sql.unsafe(sortBy)} ${sql.unsafe(sortOrder)}
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM notifications
            ${sql.unsafe(whereClause)}
        `;

        const unreadCount = await sql`
            SELECT COUNT(*) as count FROM notifications
            WHERE user_id = ${userId} AND is_read = false
        `;

        res.status(200).json({
            notifications,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            },
            unread_count: parseInt(unreadCount[0].count)
        });
    } catch (error) {
        console.error('Error getting notifications:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Mark notification as read
export async function markAsRead(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;

        const updatedNotification = await sql`
            UPDATE notifications SET
                is_read = true,
                read_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        if (updatedNotification.length === 0) {
            return res.status(404).json({ message: "Notification not found" });
        }

        res.status(200).json(updatedNotification[0]);
    } catch (error) {
        console.error('Error marking notification as read:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Mark all notifications as read
export async function markAllAsRead(req, res) {
    try {
        const userId = req.userId;

        await sql`
            UPDATE notifications SET
                is_read = true,
                read_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ${userId} AND is_read = false
        `;

        res.status(200).json({ message: "All notifications marked as read" });
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Delete notification
export async function deleteNotification(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;

        const result = await sql`
            DELETE FROM notifications 
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Notification not found" });
        }

        res.status(200).json({ message: "Notification deleted successfully" });
    } catch (error) {
        console.error('Error deleting notification:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create notification (internal function)
export async function createNotification(req, res) {
    try {
        const {
            user_id,
            type,
            title,
            message,
            data = {},
            action_url = '',
            priority = 'normal'
        } = req.body;

        if (!user_id || !type || !title || !message) {
            return res.status(400).json({ message: "Missing required fields" });
        }

        const newNotification = await sql`
            INSERT INTO notifications (
                user_id, type, title, message, data, action_url, priority
            ) VALUES (
                ${user_id}, ${type}, ${title}, ${message}, 
                ${JSON.stringify(data)}, ${action_url}, ${priority}
            ) RETURNING *
        `;

        res.status(201).json(newNotification[0]);
    } catch (error) {
        console.error('Error creating notification:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Send bulk notifications (admin only)
export async function sendBulkNotifications(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const {
            user_ids = [], // If empty, send to all users
            type,
            title,
            message,
            data = {},
            action_url = '',
            priority = 'normal'
        } = req.body;

        if (!type || !title || !message) {
            return res.status(400).json({ message: "Missing required fields" });
        }

        let targetUsers = [];

        if (user_ids.length > 0) {
            // Send to specific users
            targetUsers = user_ids;
        } else {
            // Send to all users
            const allUsers = await sql`
                SELECT id FROM users WHERE status = 'active'
            `;
            targetUsers = allUsers.map(user => user.id);
        }

        // Create notifications for all target users
        const notifications = [];
        for (const userId of targetUsers) {
            const notification = await sql`
                INSERT INTO notifications (
                    user_id, type, title, message, data, action_url, priority
                ) VALUES (
                    ${userId}, ${type}, ${title}, ${message}, 
                    ${JSON.stringify(data)}, ${action_url}, ${priority}
                ) RETURNING *
            `;
            notifications.push(notification[0]);
        }

        res.status(201).json({
            message: `${notifications.length} notifications sent successfully`,
            notifications_sent: notifications.length
        });
    } catch (error) {
        console.error('Error sending bulk notifications:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get notification statistics (admin only)
export async function getNotificationStats(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const totalNotifications = await sql`
            SELECT COUNT(*) as count FROM notifications
        `;

        const readNotifications = await sql`
            SELECT COUNT(*) as count FROM notifications WHERE is_read = true
        `;

        const unreadNotifications = await sql`
            SELECT COUNT(*) as count FROM notifications WHERE is_read = false
        `;

        const notificationsByType = await sql`
            SELECT type, COUNT(*) as count 
            FROM notifications 
            GROUP BY type 
            ORDER BY count DESC
        `;

        const notificationsByPriority = await sql`
            SELECT priority, COUNT(*) as count 
            FROM notifications 
            GROUP BY priority 
            ORDER BY count DESC
        `;

        const recentNotifications = await sql`
            SELECT COUNT(*) as count 
            FROM notifications 
            WHERE created_at >= NOW() - INTERVAL '24 hours'
        `;

        res.status(200).json({
            total_notifications: parseInt(totalNotifications[0].count),
            read_notifications: parseInt(readNotifications[0].count),
            unread_notifications: parseInt(unreadNotifications[0].count),
            recent_24h: parseInt(recentNotifications[0].count),
            by_type: notificationsByType,
            by_priority: notificationsByPriority
        });
    } catch (error) {
        console.error('Error getting notification stats:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Helper function to create system notifications
export async function createSystemNotification(userId, type, title, message, data = {}, actionUrl = '') {
    try {
        const notification = await sql`
            INSERT INTO notifications (
                user_id, type, title, message, data, action_url, priority
            ) VALUES (
                ${userId}, ${type}, ${title}, ${message}, 
                ${JSON.stringify(data)}, ${actionUrl}, 'normal'
            ) RETURNING *
        `;
        return notification[0];
    } catch (error) {
        console.error('Error creating system notification:', error);
        return null;
    }
}
