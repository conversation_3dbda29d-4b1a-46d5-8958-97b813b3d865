{"version": 3, "sources": ["../src/cookie.ts"], "sourcesContent": ["import Cookies from 'js-cookie';\n\ntype LocationAttributes = {\n  path?: string;\n  domain?: string;\n};\n\nexport function createC<PERSON><PERSON><PERSON><PERSON>ler(cookieName: string) {\n  return {\n    get() {\n      return Cookies.get(cookieName);\n    },\n    /**\n     * Setting a cookie will use some defaults such as path being set to \"/\".\n     */\n    set(newValue: string, options: Cookies.CookieAttributes = {}): void {\n      Cookies.set(cookieName, newValue, options);\n    },\n    /**\n     * On removing a cookie, you have to pass the exact same path/domain attributes used to set it initially\n     * @see https://github.com/js-cookie/js-cookie#basic-usage\n     */\n    remove(locationAttributes?: LocationAttributes) {\n      Cookies.remove(cookieName, locationAttributes);\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAAoB;AAOb,SAAS,oBAAoB,YAAoB;AACtD,SAAO;AAAA,IACL,MAAM;AACJ,aAAO,iBAAAA,QAAQ,IAAI,UAAU;AAAA,IAC/B;AAAA;AAAA;AAAA;AAAA,IAIA,IAAI,UAAkB,UAAoC,CAAC,GAAS;AAClE,uBAAAA,QAAQ,IAAI,YAAY,UAAU,OAAO;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO,oBAAyC;AAC9C,uBAAAA,QAAQ,OAAO,YAAY,kBAAkB;AAAA,IAC/C;AAAA,EACF;AACF;", "names": ["Cookies"]}