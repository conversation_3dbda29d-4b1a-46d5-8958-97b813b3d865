{"version": 3, "sources": ["../src/clerkEventBus.ts"], "sourcesContent": ["import type { ClerkEventPayload } from '@clerk/types';\n\nimport { createEventBus } from './eventBus';\n\nexport const clerkEvents = {\n  Status: 'status',\n} satisfies Record<string, keyof ClerkEventPayload>;\n\nexport const createClerkEventBus = () => {\n  return createEventBus<ClerkEventPayload>();\n};\n"], "mappings": ";;;;;;AAIO,IAAM,cAAc;AAAA,EACzB,QAAQ;AACV;AAEO,IAAM,sBAAsB,MAAM;AACvC,SAAO,eAAkC;AAC3C;", "names": []}