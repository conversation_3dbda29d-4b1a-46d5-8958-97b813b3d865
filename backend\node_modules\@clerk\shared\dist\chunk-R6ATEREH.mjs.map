{"version": 3, "sources": ["../src/loadClerkJsScript.ts"], "sourcesContent": ["import type { ClerkOptions, SDKMetadata, Without } from '@clerk/types';\n\nimport { buildErrorThrower } from './error';\nimport { createDevOrStagingUrlCache, parsePublishableKey } from './keys';\nimport { loadScript } from './loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from './proxy';\nimport { addClerkPrefix } from './url';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/shared' });\n\n/**\n * Sets the package name for error messages during ClerkJS script loading.\n *\n * @example\n * setClerkJsLoadingErrorPackageName('@clerk/clerk-react');\n */\nexport function setClerkJsLoadingErrorPackageName(packageName: string) {\n  errorThrower.setPackageName({ packageName });\n}\n\ntype LoadClerkJsScriptOptions = Without<ClerkOptions, 'isSatellite'> & {\n  publishableKey: string;\n  clerkJSUrl?: string;\n  clerkJSVariant?: 'headless' | '';\n  clerkJSVersion?: string;\n  sdkMetadata?: SDKMetadata;\n  proxyUrl?: string;\n  domain?: string;\n  nonce?: string;\n};\n\n/**\n * Hotloads the Clerk JS script.\n *\n * Checks for an existing Clerk JS script. If found, it returns a promise\n * that resolves when the script loads. If not found, it uses the provided options to\n * build the Clerk JS script URL and load the script.\n *\n * @param opts - The options used to build the Clerk JS script URL and load the script.\n *               Must include a `publishableKey` if no existing script is found.\n *\n * @example\n * loadClerkJsScript({ publishableKey: 'pk_' });\n */\nconst loadClerkJsScript = async (opts?: LoadClerkJsScriptOptions) => {\n  const existingScript = document.querySelector<HTMLScriptElement>('script[data-clerk-js-script]');\n\n  if (existingScript) {\n    return new Promise((resolve, reject) => {\n      existingScript.addEventListener('load', () => {\n        resolve(existingScript);\n      });\n\n      existingScript.addEventListener('error', () => {\n        reject(FAILED_TO_LOAD_ERROR);\n      });\n    });\n  }\n\n  if (!opts?.publishableKey) {\n    errorThrower.throwMissingPublishableKeyError();\n    return;\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    nonce: opts.nonce,\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\n/**\n * Generates a Clerk JS script URL.\n *\n * @param opts - The options to use when building the Clerk JS script URL.\n *\n * @example\n * clerkJsScriptUrl({ publishableKey: 'pk_' });\n */\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\n/**\n * Builds an object of Clerk JS script attributes.\n */\nconst buildClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => {\n  const obj: Record<string, string> = {};\n\n  if (options.publishableKey) {\n    obj['data-clerk-publishable-key'] = options.publishableKey;\n  }\n\n  if (options.proxyUrl) {\n    obj['data-clerk-proxy-url'] = options.proxyUrl;\n  }\n\n  if (options.domain) {\n    obj['data-clerk-domain'] = options.domain;\n  }\n\n  if (options.nonce) {\n    obj.nonce = options.nonce;\n  }\n\n  return obj;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const attributes = buildClerkJsScriptAttributes(options);\n  for (const attribute in attributes) {\n    script.setAttribute(attribute, attributes[attribute]);\n  }\n};\n\nexport { loadClerkJsScript, buildClerkJsScriptAttributes, clerkJsScriptUrl };\nexport type { LoadClerkJsScriptOptions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,uBAAuB;AAE7B,IAAM,EAAE,kBAAkB,IAAI,2BAA2B;AAEzD,IAAM,eAAe,kBAAkB,EAAE,aAAa,gBAAgB,CAAC;AAQhE,SAAS,kCAAkC,aAAqB;AACrE,eAAa,eAAe,EAAE,YAAY,CAAC;AAC7C;AA0BA,IAAM,oBAAoB,OAAO,SAAoC;AACnE,QAAM,iBAAiB,SAAS,cAAiC,8BAA8B;AAE/F,MAAI,gBAAgB;AAClB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,qBAAe,iBAAiB,QAAQ,MAAM;AAC5C,gBAAQ,cAAc;AAAA,MACxB,CAAC;AAED,qBAAe,iBAAiB,SAAS,MAAM;AAC7C,eAAO,oBAAoB;AAAA,MAC7B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,MAAI,CAAC,MAAM,gBAAgB;AACzB,iBAAa,gCAAgC;AAC7C;AAAA,EACF;AAEA,SAAO,WAAW,iBAAiB,IAAI,GAAG;AAAA,IACxC,OAAO;AAAA,IACP,aAAa;AAAA,IACb,OAAO,KAAK;AAAA,IACZ,YAAY,6BAA6B,IAAI;AAAA,EAC/C,CAAC,EAAE,MAAM,MAAM;AACb,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC,CAAC;AACH;AAUA,IAAM,mBAAmB,CAAC,SAAmC;AAC3D,QAAM,EAAE,YAAY,gBAAgB,gBAAgB,UAAU,QAAQ,eAAe,IAAI;AAEzF,MAAI,YAAY;AACd,WAAO;AAAA,EACT;AAEA,MAAI,aAAa;AACjB,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,GAAG;AAC3C,iBAAa,sBAAsB,QAAQ,EAAE,QAAQ,iBAAiB,EAAE;AAAA,EAC1E,WAAW,UAAU,CAAC,kBAAkB,oBAAoB,cAAc,GAAG,eAAe,EAAE,GAAG;AAC/F,iBAAa,eAAe,MAAM;AAAA,EACpC,OAAO;AACL,iBAAa,oBAAoB,cAAc,GAAG,eAAe;AAAA,EACnE;AAEA,QAAM,UAAU,iBAAiB,GAAG,eAAe,QAAQ,QAAQ,EAAE,CAAC,MAAM;AAC5E,QAAM,UAAU,gBAAgB,cAAc;AAC9C,SAAO,WAAW,UAAU,wBAAwB,OAAO,eAAe,OAAO;AACnF;AAKA,IAAM,+BAA+B,CAAC,YAAsC;AAC1E,QAAM,MAA8B,CAAC;AAErC,MAAI,QAAQ,gBAAgB;AAC1B,QAAI,4BAA4B,IAAI,QAAQ;AAAA,EAC9C;AAEA,MAAI,QAAQ,UAAU;AACpB,QAAI,sBAAsB,IAAI,QAAQ;AAAA,EACxC;AAEA,MAAI,QAAQ,QAAQ;AAClB,QAAI,mBAAmB,IAAI,QAAQ;AAAA,EACrC;AAEA,MAAI,QAAQ,OAAO;AACjB,QAAI,QAAQ,QAAQ;AAAA,EACtB;AAEA,SAAO;AACT;AAEA,IAAM,+BAA+B,CAAC,YAAsC,CAAC,WAA8B;AACzG,QAAM,aAAa,6BAA6B,OAAO;AACvD,aAAW,aAAa,YAAY;AAClC,WAAO,aAAa,WAAW,WAAW,SAAS,CAAC;AAAA,EACtD;AACF;", "names": []}