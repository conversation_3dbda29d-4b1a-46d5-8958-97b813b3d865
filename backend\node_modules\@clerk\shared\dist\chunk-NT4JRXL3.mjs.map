{"version": 3, "sources": ["../src/error.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n  retryAfter?: number;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isReverificationCancelledError(err: any) {\n  return isClerkRuntimeError(err) && err.code === 'reverification_cancelled';\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON {\n  return {\n    code: error?.code || '',\n    message: error?.message || '',\n    long_message: error?.longMessage,\n    meta: {\n      param_name: error?.meta?.paramName,\n      session_id: error?.meta?.sessionId,\n      email_addresses: error?.meta?.emailAddresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n  retryAfter?: number;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.retryAfter = retryAfter;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, can be used for localization.\n   *\n   * @type {string}\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    const prefix = '🔒 Clerk:';\n    const regex = new RegExp(prefix.replace(' ', '\\\\s*'), 'i');\n    const sanitized = message.replace(regex, '');\n    const _message = `${prefix} ${sanitized.trim()}\\n\\n(code=\"${code}\")\\n`;\n    super(_message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = _message;\n    this.clerkRuntimeError = true;\n    this.name = 'ClerkRuntimeError';\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    this.name = 'EmailLinkError' as const;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err.name === 'EmailLinkError';\n}\n\n/**\n * @deprecated Use `EmailLinkErrorCodeStatus` instead.\n *\n * @hidden\n */\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n};\n\nexport const EmailLinkErrorCodeStatus = {\n  Expired: 'expired',\n  Failed: 'failed',\n  ClientMismatch: 'client_mismatch',\n} as const;\n\nconst DefaultMessages = Object.freeze({\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingSecretKeyErrorMessage: `Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n  MissingClerkProvider: `{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n\n  throwMissingSecretKeyError(): never;\n\n  throwMissingClerkProviderError(params: { source?: string }): never;\n\n  throw(message: string): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n\n    throwMissingSecretKeyError(): never {\n      throw new Error(buildMessage(messages.MissingSecretKeyErrorMessage));\n    },\n\n    throwMissingClerkProviderError(params: { source?: string }): never {\n      throw new Error(buildMessage(messages.MissingClerkProvider, params));\n    },\n\n    throw(message: string): never {\n      throw new Error(buildMessage(message));\n    },\n  };\n}\n\ntype ClerkWebAuthnErrorCode =\n  // Generic\n  | 'passkey_not_supported'\n  | 'passkey_pa_not_supported'\n  | 'passkey_invalid_rpID_or_domain'\n  | 'passkey_already_exists'\n  | 'passkey_operation_aborted'\n  // Retrieval\n  | 'passkey_retrieval_cancelled'\n  | 'passkey_retrieval_failed'\n  // Registration\n  | 'passkey_registration_cancelled'\n  | 'passkey_registration_failed';\n\nexport class ClerkWebAuthnError extends ClerkRuntimeError {\n  /**\n   * A unique code identifying the error, can be used for localization.\n   */\n  code: ClerkWebAuthnErrorCode;\n\n  constructor(message: string, { code }: { code: ClerkWebAuthnErrorCode }) {\n    super(message, { code });\n    this.code = code;\n  }\n}\n"], "mappings": ";AAEO,SAAS,oBAAoB,GAAiB;AACnD,QAAM,SAAS,GAAG;AAClB,QAAM,OAAO,GAAG,SAAS,CAAC,GAAG;AAC7B,SAAO,SAAS,4BAA4B,WAAW;AACzD;AAEO,SAAS,eAAe,GAAmC;AAChE,SAAO,CAAC,mBAAmB,uBAAuB,uBAAuB,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI;AACtG;AAEO,SAAS,WAAW,GAAiB;AAC1C,QAAM,SAAS,GAAG;AAClB,SAAO,CAAC,CAAC,UAAU,UAAU,OAAO,SAAS;AAC/C;AAEO,SAAS,eAAe,GAAiB;AAE9C,QAAM,WAAW,GAAG,EAAE,OAAO,GAAG,EAAE,IAAI,MAAM,IAAI,YAAY,EAAE,QAAQ,QAAQ,EAAE;AAChF,SAAO,QAAQ,SAAS,cAAc;AACxC;AAiBO,SAAS,aAAa,OAAgF;AAC3G,SAAO,wBAAwB,KAAK,KAAK,gBAAgB,KAAK,KAAK,oBAAoB,KAAK;AAC9F;AAEO,SAAS,wBAAwB,KAAwC;AAC9E,SAAO,gBAAgB;AACzB;AAkBO,SAAS,oBAAoB,KAAoC;AACtE,SAAO,uBAAuB;AAChC;AAEO,SAAS,+BAA+B,KAAU;AACvD,SAAO,oBAAoB,GAAG,KAAK,IAAI,SAAS;AAClD;AAEO,SAAS,gBAAgB,KAAgC;AAC9D,SAAO,UAAU,OAAO,CAAC,MAAM,OAAO,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,aAAa;AAClF;AAEO,SAAS,kBAAkB,KAAU;AAC1C,SAAO,wBAAwB,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,SAAS;AACnE;AAEO,SAAS,qBAAqB,KAAU;AAC7C,SAAO,wBAAwB,GAAG,KAAK,IAAI,SAAS,CAAC,GAAG,SAAS;AACnE;AAEO,SAAS,YAAY,OAA4B,CAAC,GAAoB;AAC3E,SAAO,KAAK,SAAS,IAAI,KAAK,IAAI,UAAU,IAAI,CAAC;AACnD;AAEO,SAAS,WAAW,OAAyC;AAClE,SAAO;AAAA,IACL,MAAM,MAAM;AAAA,IACZ,SAAS,MAAM;AAAA,IACf,aAAa,MAAM;AAAA,IACnB,MAAM;AAAA,MACJ,WAAW,OAAO,MAAM;AAAA,MACxB,WAAW,OAAO,MAAM;AAAA,MACxB,gBAAgB,OAAO,MAAM;AAAA,MAC7B,aAAa,OAAO,MAAM;AAAA,MAC1B,QAAQ,OAAO,MAAM;AAAA,IACvB;AAAA,EACF;AACF;AAEO,SAAS,YAAY,OAAgD;AAC1E,SAAO;AAAA,IACL,MAAM,OAAO,QAAQ;AAAA,IACrB,SAAS,OAAO,WAAW;AAAA,IAC3B,cAAc,OAAO;AAAA,IACrB,MAAM;AAAA,MACJ,YAAY,OAAO,MAAM;AAAA,MACzB,YAAY,OAAO,MAAM;AAAA,MACzB,iBAAiB,OAAO,MAAM;AAAA,MAC9B,aAAa,OAAO,MAAM;AAAA,MAC1B,QAAQ,OAAO,MAAM;AAAA,IACvB;AAAA,EACF;AACF;AAEO,IAAM,wBAAN,MAAM,+BAA8B,MAAM;AAAA,EAU/C,YAAY,SAAiB,EAAE,MAAM,QAAQ,cAAc,WAAW,GAA4B;AAChG,UAAM,OAAO;AAYf,SAAO,WAAW,MAAM;AACtB,UAAI,UAAU,IAAI,KAAK,IAAI;AAAA,UAAc,KAAK,OAAO;AAAA,SAAY,KAAK,MAAM;AAAA,qBAAwB,KAAK,OAAO;AAAA,QAC9G,OAAK,KAAK,UAAU,CAAC;AAAA,MACvB,CAAC;AAED,UAAI,KAAK,cAAc;AACrB,mBAAW;AAAA,kBAAqB,KAAK,YAAY;AAAA,MACnD;AAEA,aAAO;AAAA,IACT;AApBE,WAAO,eAAe,MAAM,uBAAsB,SAAS;AAE3D,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,SAAS,YAAY,IAAI;AAAA,EAChC;AAaF;AASO,IAAM,oBAAN,MAAM,2BAA0B,MAAM;AAAA,EAiB3C,YAAY,SAAiB,EAAE,KAAK,GAAqB;AACvD,UAAM,SAAS;AACf,UAAM,QAAQ,IAAI,OAAO,OAAO,QAAQ,KAAK,MAAM,GAAG,GAAG;AACzD,UAAM,YAAY,QAAQ,QAAQ,OAAO,EAAE;AAC3C,UAAM,WAAW,GAAG,MAAM,IAAI,UAAU,KAAK,CAAC;AAAA;AAAA,SAAc,IAAI;AAAA;AAChE,UAAM,QAAQ;AAehB;AAAA;AAAA;AAAA;AAAA;AAAA,SAAO,WAAW,MAAM;AACtB,aAAO,IAAI,KAAK,IAAI;AAAA,UAAc,KAAK,OAAO;AAAA,IAChD;AAfE,WAAO,eAAe,MAAM,mBAAkB,SAAS;AAEvD,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,oBAAoB;AACzB,SAAK,OAAO;AAAA,EACd;AAUF;AAEO,IAAM,iBAAN,MAAM,wBAAuB,MAAM;AAAA,EAGxC,YAAY,MAAc;AACxB,UAAM,IAAI;AACV,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,WAAO,eAAe,MAAM,gBAAe,SAAS;AAAA,EACtD;AACF;AAEO,SAAS,iBAAiB,KAAmC;AAClE,SAAO,IAAI,SAAS;AACtB;AAOO,IAAM,qBAAqB;AAAA,EAChC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAClB;AAEO,IAAM,2BAA2B;AAAA,EACtC,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,gBAAgB;AAClB;AAEA,IAAM,kBAAkB,OAAO,OAAO;AAAA,EACpC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,sBAAsB;AACxB,CAAC;AA+BM,SAAS,kBAAkB,EAAE,aAAa,eAAe,GAAsC;AACpG,MAAI,MAAM;AAEV,QAAM,WAAW;AAAA,IACf,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AAEA,WAAS,aAAa,YAAoB,cAAgD;AACxF,QAAI,CAAC,cAAc;AACjB,aAAO,GAAG,GAAG,KAAK,UAAU;AAAA,IAC9B;AAEA,QAAI,MAAM;AACV,UAAM,UAAU,WAAW,SAAS,uBAAuB;AAE3D,eAAW,SAAS,SAAS;AAC3B,YAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,IAAI,SAAS;AAC5D,YAAM,IAAI,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM,WAAW;AAAA,IAClD;AAEA,WAAO,GAAG,GAAG,KAAK,GAAG;AAAA,EACvB;AAEA,SAAO;AAAA,IACL,eAAe,EAAE,aAAAA,aAAY,GAAsC;AACjE,UAAI,OAAOA,iBAAgB,UAAU;AACnC,cAAMA;AAAA,MACR;AACA,aAAO;AAAA,IACT;AAAA,IAEA,YAAY,EAAE,gBAAAC,gBAAe,GAAsC;AACjE,aAAO,OAAO,UAAUA,mBAAkB,CAAC,CAAC;AAC5C,aAAO;AAAA,IACT;AAAA,IAEA,gCAAgC,QAAiC;AAC/D,YAAM,IAAI,MAAM,aAAa,SAAS,mCAAmC,MAAM,CAAC;AAAA,IAClF;AAAA,IAEA,qBAAqB,QAAiC;AACpD,YAAM,IAAI,MAAM,aAAa,SAAS,6BAA6B,MAAM,CAAC;AAAA,IAC5E;AAAA,IAEA,kCAAyC;AACvC,YAAM,IAAI,MAAM,aAAa,SAAS,iCAAiC,CAAC;AAAA,IAC1E;AAAA,IAEA,6BAAoC;AAClC,YAAM,IAAI,MAAM,aAAa,SAAS,4BAA4B,CAAC;AAAA,IACrE;AAAA,IAEA,+BAA+B,QAAoC;AACjE,YAAM,IAAI,MAAM,aAAa,SAAS,sBAAsB,MAAM,CAAC;AAAA,IACrE;AAAA,IAEA,MAAM,SAAwB;AAC5B,YAAM,IAAI,MAAM,aAAa,OAAO,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AAgBO,IAAM,qBAAN,cAAiC,kBAAkB;AAAA,EAMxD,YAAY,SAAiB,EAAE,KAAK,GAAqC;AACvE,UAAM,SAAS,EAAE,KAAK,CAAC;AACvB,SAAK,OAAO;AAAA,EACd;AACF;", "names": ["packageName", "customMessages"]}