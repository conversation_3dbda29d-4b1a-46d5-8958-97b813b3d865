{"version": 3, "sources": ["../src/date.ts"], "sourcesContent": ["const MILLISECONDS_IN_DAY = 86400000;\n\nexport function dateTo12HourTime(date: Date): string {\n  if (!date) {\n    return '';\n  }\n  return date.toLocaleString('en-US', {\n    hour: '2-digit',\n    minute: 'numeric',\n    hour12: true,\n  });\n}\n\nexport function differenceInCalendarDays(a: Date, b: Date, { absolute = true } = {}): number {\n  if (!a || !b) {\n    return 0;\n  }\n  const utcA = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n  const utcB = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n  const diff = Math.floor((utcB - utcA) / MILLISECONDS_IN_DAY);\n  return absolute ? Math.abs(diff) : diff;\n}\n\nexport function normalizeDate(d: Date | string | number): Date {\n  try {\n    return new Date(d || new Date());\n  } catch {\n    return new Date();\n  }\n}\n\ntype DateFormatRelativeParams = {\n  date: Date | string | number;\n  relativeTo: Date | string | number;\n};\n\nexport type RelativeDateCase = 'previous6Days' | 'lastDay' | 'sameDay' | 'nextDay' | 'next6Days' | 'other';\ntype RelativeDateReturn = { relativeDateCase: RelativeDateCase; date: Date } | null;\n\nexport function formatRelative(props: DateFormatRelativeParams): RelativeDateReturn {\n  const { date, relativeTo } = props;\n  if (!date || !relativeTo) {\n    return null;\n  }\n  const a = normalizeDate(date);\n  const b = normalizeDate(relativeTo);\n  const differenceInDays = differenceInCalendarDays(b, a, { absolute: false });\n\n  if (differenceInDays < -6) {\n    return { relativeDateCase: 'other', date: a };\n  }\n  if (differenceInDays < -1) {\n    return { relativeDateCase: 'previous6Days', date: a };\n  }\n  if (differenceInDays === -1) {\n    return { relativeDateCase: 'lastDay', date: a };\n  }\n  if (differenceInDays === 0) {\n    return { relativeDateCase: 'sameDay', date: a };\n  }\n  if (differenceInDays === 1) {\n    return { relativeDateCase: 'nextDay', date: a };\n  }\n  if (differenceInDays < 7) {\n    return { relativeDateCase: 'next6Days', date: a };\n  }\n  return { relativeDateCase: 'other', date: a };\n}\n\nexport function addYears(initialDate: Date | number | string, yearsToAdd: number): Date {\n  const date = normalizeDate(initialDate);\n  date.setFullYear(date.getFullYear() + yearsToAdd);\n  return date;\n}\n"], "mappings": ";AAAA,IAAM,sBAAsB;AAErB,SAAS,iBAAiB,MAAoB;AACnD,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AACA,SAAO,KAAK,eAAe,SAAS;AAAA,IAClC,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV,CAAC;AACH;AAEO,SAAS,yBAAyB,GAAS,GAAS,EAAE,WAAW,KAAK,IAAI,CAAC,GAAW;AAC3F,MAAI,CAAC,KAAK,CAAC,GAAG;AACZ,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,IAAI,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ,CAAC;AAChE,QAAM,OAAO,KAAK,IAAI,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,EAAE,QAAQ,CAAC;AAChE,QAAM,OAAO,KAAK,OAAO,OAAO,QAAQ,mBAAmB;AAC3D,SAAO,WAAW,KAAK,IAAI,IAAI,IAAI;AACrC;AAEO,SAAS,cAAc,GAAiC;AAC7D,MAAI;AACF,WAAO,IAAI,KAAK,KAAK,oBAAI,KAAK,CAAC;AAAA,EACjC,QAAQ;AACN,WAAO,oBAAI,KAAK;AAAA,EAClB;AACF;AAUO,SAAS,eAAe,OAAqD;AAClF,QAAM,EAAE,MAAM,WAAW,IAAI;AAC7B,MAAI,CAAC,QAAQ,CAAC,YAAY;AACxB,WAAO;AAAA,EACT;AACA,QAAM,IAAI,cAAc,IAAI;AAC5B,QAAM,IAAI,cAAc,UAAU;AAClC,QAAM,mBAAmB,yBAAyB,GAAG,GAAG,EAAE,UAAU,MAAM,CAAC;AAE3E,MAAI,mBAAmB,IAAI;AACzB,WAAO,EAAE,kBAAkB,SAAS,MAAM,EAAE;AAAA,EAC9C;AACA,MAAI,mBAAmB,IAAI;AACzB,WAAO,EAAE,kBAAkB,iBAAiB,MAAM,EAAE;AAAA,EACtD;AACA,MAAI,qBAAqB,IAAI;AAC3B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,qBAAqB,GAAG;AAC1B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,qBAAqB,GAAG;AAC1B,WAAO,EAAE,kBAAkB,WAAW,MAAM,EAAE;AAAA,EAChD;AACA,MAAI,mBAAmB,GAAG;AACxB,WAAO,EAAE,kBAAkB,aAAa,MAAM,EAAE;AAAA,EAClD;AACA,SAAO,EAAE,kBAAkB,SAAS,MAAM,EAAE;AAC9C;AAEO,SAAS,SAAS,aAAqC,YAA0B;AACtF,QAAM,OAAO,cAAc,WAAW;AACtC,OAAK,YAAY,KAAK,YAAY,IAAI,UAAU;AAChD,SAAO;AACT;", "names": []}