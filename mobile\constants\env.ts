/**
 * Environment Variables
 * Expo environment variables configuration
 */

// Clerk Configuration
export const CLERK_PUBLISHABLE_KEY = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY || '';

// API Configuration
export const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:5001/api';

// Validate required environment variables
if (!CLERK_PUBLISHABLE_KEY) {
  console.warn('EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY is not set. Please add it to your .env file.');
}

export default {
  CLERK_PUBLISHABLE_KEY,
  API_BASE_URL,
};
