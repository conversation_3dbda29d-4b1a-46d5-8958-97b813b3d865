import { sql } from "../config/db.js";

// Get all lotteries
export async function getLotteries(req, res) {
    try {
        const { 
            page = 1, 
            limit = 10, 
            status = 'active',
            search,
            sortBy = 'draw_date',
            sortOrder = 'ASC'
        } = req.query;

        const offset = (page - 1) * limit;
        
        let whereConditions = [];
        if (status) whereConditions.push(`status = '${status}'`);
        if (search) {
            whereConditions.push(`(title ILIKE '%${search}%' OR description ILIKE '%${search}%')`);
        }
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const lotteries = await sql`
            SELECT * FROM lotteries
            ${sql.unsafe(whereClause)}
            ORDER BY ${sql.unsafe(sortBy)} ${sql.unsafe(sortOrder)}
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM lotteries
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            lotteries,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting lotteries:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get single lottery
export async function getLotteryById(req, res) {
    try {
        const { id } = req.params;

        const lottery = await sql`
            SELECT * FROM lotteries WHERE id = ${id}
        `;

        if (lottery.length === 0) {
            return res.status(404).json({ message: "Lottery not found" });
        }

        res.status(200).json(lottery[0]);
    } catch (error) {
        console.error('Error getting lottery:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create lottery (admin only)
export async function createLottery(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const {
            title,
            description,
            ticket_price,
            max_tickets,
            draw_date,
            prizes,
            rules = {},
            images = []
        } = req.body;

        if (!title || !description || !ticket_price || !max_tickets || !draw_date || !prizes) {
            return res.status(400).json({ message: "Missing required fields" });
        }

        const newLottery = await sql`
            INSERT INTO lotteries (
                title, description, ticket_price, max_tickets, draw_date,
                prizes, rules, images, status, tickets_sold, participants
            ) VALUES (
                ${title}, ${description}, ${ticket_price}, ${max_tickets}, ${draw_date},
                ${JSON.stringify(prizes)}, ${JSON.stringify(rules)}, ${JSON.stringify(images)},
                'active', 0, '[]'
            ) RETURNING *
        `;

        res.status(201).json(newLottery[0]);
    } catch (error) {
        console.error('Error creating lottery:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Buy lottery ticket
export async function buyTicket(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;
        const { quantity = 1 } = req.body;

        if (quantity < 1) {
            return res.status(400).json({ message: "Quantity must be at least 1" });
        }

        // Get lottery details
        const lottery = await sql`
            SELECT * FROM lotteries WHERE id = ${id}
        `;

        if (lottery.length === 0) {
            return res.status(404).json({ message: "Lottery not found" });
        }

        const lotteryData = lottery[0];

        // Check if lottery is active
        if (lotteryData.status !== 'active') {
            return res.status(400).json({ message: "Lottery is not active" });
        }

        // Check if lottery has ended
        if (new Date() > new Date(lotteryData.draw_date)) {
            return res.status(400).json({ message: "Lottery has ended" });
        }

        // Check if enough tickets available
        if (lotteryData.tickets_sold + quantity > lotteryData.max_tickets) {
            return res.status(400).json({ message: "Not enough tickets available" });
        }

        // Check user permissions
        const user = await sql`
            SELECT can_participate_in_lottery FROM users WHERE id = ${userId}
        `;

        if (user.length === 0 || !user[0].can_participate_in_lottery) {
            return res.status(403).json({ message: "You are not allowed to participate in lotteries" });
        }

        // Generate ticket numbers
        const ticketNumbers = [];
        for (let i = 0; i < quantity; i++) {
            const ticketNumber = `${id}-${lotteryData.tickets_sold + i + 1}`.padStart(10, '0');
            ticketNumbers.push(ticketNumber);
        }

        // Create ticket record
        const newTicket = await sql`
            INSERT INTO tickets (
                lottery_id, user_id, ticket_numbers, quantity, 
                total_price, purchase_date
            ) VALUES (
                ${id}, ${userId}, ${JSON.stringify(ticketNumbers)}, ${quantity},
                ${lotteryData.ticket_price * quantity}, CURRENT_TIMESTAMP
            ) RETURNING *
        `;

        // Update lottery
        const participants = JSON.parse(lotteryData.participants || '[]');
        const existingParticipant = participants.find(p => p.user_id === userId);
        
        if (existingParticipant) {
            existingParticipant.tickets += quantity;
            existingParticipant.ticket_numbers.push(...ticketNumbers);
        } else {
            participants.push({
                user_id: userId,
                tickets: quantity,
                ticket_numbers: ticketNumbers
            });
        }

        await sql`
            UPDATE lotteries SET
                tickets_sold = tickets_sold + ${quantity},
                participants = ${JSON.stringify(participants)},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
        `;

        res.status(201).json({
            ticket: newTicket[0],
            message: "Tickets purchased successfully"
        });
    } catch (error) {
        console.error('Error buying ticket:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Draw lottery (admin only)
export async function drawLottery(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const { id } = req.params;

        // Get lottery details
        const lottery = await sql`
            SELECT * FROM lotteries WHERE id = ${id}
        `;

        if (lottery.length === 0) {
            return res.status(404).json({ message: "Lottery not found" });
        }

        const lotteryData = lottery[0];

        if (lotteryData.status !== 'active') {
            return res.status(400).json({ message: "Lottery is not active" });
        }

        // Get all tickets for this lottery
        const tickets = await sql`
            SELECT * FROM tickets WHERE lottery_id = ${id}
        `;

        if (tickets.length === 0) {
            return res.status(400).json({ message: "No tickets sold for this lottery" });
        }

        // Collect all ticket numbers
        const allTicketNumbers = [];
        tickets.forEach(ticket => {
            const ticketNumbers = JSON.parse(ticket.ticket_numbers);
            ticketNumbers.forEach(number => {
                allTicketNumbers.push({
                    number: number,
                    user_id: ticket.user_id
                });
            });
        });

        // Draw winners based on prizes
        const prizes = JSON.parse(lotteryData.prizes);
        const winners = [];

        for (const prize of prizes) {
            if (allTicketNumbers.length === 0) break;
            
            const randomIndex = Math.floor(Math.random() * allTicketNumbers.length);
            const winningTicket = allTicketNumbers[randomIndex];
            
            winners.push({
                prize: prize,
                winner_user_id: winningTicket.user_id,
                winning_ticket: winningTicket.number
            });

            // Remove winning ticket from pool
            allTicketNumbers.splice(randomIndex, 1);
        }

        // Update lottery with results
        await sql`
            UPDATE lotteries SET
                status = 'completed',
                winners = ${JSON.stringify(winners)},
                draw_completed_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
        `;

        res.status(200).json({
            lottery_id: id,
            winners: winners,
            message: "Lottery drawn successfully"
        });
    } catch (error) {
        console.error('Error drawing lottery:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get user's lottery tickets
export async function getUserTickets(req, res) {
    try {
        const userId = req.userId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const tickets = await sql`
            SELECT t.*, l.title as lottery_title, l.draw_date, l.status as lottery_status
            FROM tickets t
            JOIN lotteries l ON t.lottery_id = l.id
            WHERE t.user_id = ${userId}
            ORDER BY t.purchase_date DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM tickets WHERE user_id = ${userId}
        `;

        res.status(200).json({
            tickets,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting user tickets:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
