@import "tailwindcss";

:root {
  --foreground-rgb: 34, 34, 34;
  --background-rgb: 245, 245, 245;
  --card-bg: 255, 255, 255;
  --border-color: 230, 230, 230;
  --primary: 0, 0, 0;
  --primary-hover: 34, 34, 34;
  --accent: 255, 255, 255;
}

html.dark {
  --foreground-rgb: 245, 245, 245;
  --background-rgb: 18, 18, 18;
  --card-bg: 24, 24, 24;
  --border-color: 38, 38, 38;
  --primary: 255, 255, 255;
  --primary-hover: 200, 200, 200;
  --accent: 34, 34, 34;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-rgb: 24, 24, 27;
    --card-bg: 36, 36, 40;
    --border-color: 55, 65, 81;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'Inter', Arial, sans-serif;
  transition: background-color 0.3s, color 0.3s;
}

/* Otomatik kontrast sağlayan sınıflar */
.auto-text {
  color: rgb(var(--foreground-rgb));
}

.auto-bg {
  background-color: rgb(var(--background-rgb));
}

.auto-card {
  background-color: rgb(var(--card-bg));
  color: rgb(var(--foreground-rgb));
  border: 5px solid rgb(var(--border-color));
}

.bg-dark {
  background-color: rgb(0, 0, 0);
  color: rgb(255, 255, 255);
}

.bg-light {
  background-color: rgb(255, 255, 255);
  color: rgb(0, 0, 0);
}

.card {
  background-color: rgb(var(--card-bg));
  border: 5px solid rgb(var(--border-color));
  border-radius: 1rem;
  box-shadow: 0 2px 8px 0 rgba(71, 51, 51, 0.03);
  padding: 1.5rem;
  transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
}

button, .btn {
  background: rgb(var(--primary));
  color: rgb(var(--accent));
  border-radius: 9999px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border: none;
  transition: background-color 0.2s, color 0.2s;
}
button:hover, .btn:hover {
  background: rgb(var(--primary-hover));
  color: rgb(var(--accent));
}

.action-btn {
  border-radius: 9999px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-btn {
  background: rgb(var(--primary));
  color: rgb(var(--accent));
}
.primary-btn:hover {
  opacity: 0.9;
}

input, textarea, select {
  background: rgb(var(--card-bg));
  color: rgb(var(--foreground-rgb));
  border: 1px solid rgb(var(--border-color));
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  outline: none;
  transition: border-color 0.2s;
}
input:focus, textarea:focus, select:focus {
  border-color: rgb(var(--primary));
}

hr {
  border-color: rgb(var(--border-color));
}

::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: rgb(var(--border-color));
  border-radius: 8px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: rgb(var(--foreground-rgb));
  letter-spacing: -0.01em;
}

/* Prose dark mode */
html.dark .prose {
  color: rgb(81, 84, 87);
}
html.dark .prose h1,
html.dark .prose h2,
html.dark .prose h3,
html.dark .prose h4 {
  color: rgb(34, 33, 33);
}
html.dark .prose a {
  color: rgb(96, 165, 250);
}
html.dark .prose strong {
  color: rgb(9, 55, 129);
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Standart resim boyutları */
.product-image-container {
  position: relative;
  width: 100%;
  height: 14rem; /* h-56 */
  overflow: hidden;
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  border-radius: 0.5rem;
}

.dark .product-image-container {
  background-color: rgb(55, 65, 81); /* dark:bg-gray-700 */
}

.product-image {
  object-fit: contain;
  padding: 0.5rem;
  transition: transform 0.3s ease;
}

.product-image:hover {
  transform: scale(1.05);
}

/* Kart boyutları standartlaştırma */
.product-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 28rem; /* Minimum kart yüksekliği */
}

.product-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Resim aspect ratio koruması */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-16-9 {
  aspect-ratio: 16 / 9;
}
