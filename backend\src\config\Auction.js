/**
 * Auction Entity (Domain Object)
 * İş mantığından bağımsız açık artırma varlığı
 */
import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

const { AuctionStatus } = require('../../shared/enums');
const { AUCTION, VALIDATION } = require('../../shared/constants');

class Auction {
  constructor({
    id = null,
    productId,
    sellerId,
    title,
    description,
    startingPrice,
    currentPrice = null,
    buyNowPrice = null,
    minBidIncrement = AUCTION.MIN_BID_INCREMENT,
    startTime,
    endTime,
    status = AuctionStatus.PENDING,
    bids = [],
    winnerId = null,
    winningBid = null,
    autoExtend = true,
    extendMinutes = AUCTION.AUTO_EXTEND_MINUTES,
    viewCount = 0,
    watcherCount = 0,
    createdAt = new Date(),
    updatedAt = new Date()
  }) {
    this.id = id;
    this.productId = productId;
    this.sellerId = sellerId;
    this.title = title;
    this.description = description;
    this.startingPrice = startingPrice;
    this.currentPrice = currentPrice || startingPrice;
    this.buyNowPrice = buyNowPrice;
    this.minBidIncrement = minBidIncrement;
    this.startTime = new Date(startTime);
    this.endTime = new Date(endTime);
    this.status = status;
    this.bids = bids;
    this.winnerId = winnerId;
    this.winningBid = winningBid;
    this.autoExtend = autoExtend;
    this.extendMinutes = extendMinutes;
    this.viewCount = viewCount;
    this.watcherCount = watcherCount;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;

    // Validation
    this.validate();
  }

  /**
   * Entity validation
   */
  validate() {
    if (!this.productId) {
      throw new Error('Product ID is required');
    }

    if (!this.sellerId) {
      throw new Error('Seller ID is required');
    }

    if (!this.title || this.title.length < VALIDATION.NAME_MIN_LENGTH) {
      throw new Error(`Title must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
    }

    if (this.title.length > VALIDATION.TITLE_MAX_LENGTH) {
      throw new Error(`Title cannot exceed ${VALIDATION.TITLE_MAX_LENGTH} characters`);
    }

    if (this.startingPrice <= 0) {
      throw new Error('Starting price must be greater than 0');
    }

    if (this.buyNowPrice && this.buyNowPrice <= this.startingPrice) {
      throw new Error('Buy now price must be greater than starting price');
    }

    if (this.minBidIncrement <= 0) {
      throw new Error('Minimum bid increment must be greater than 0');
    }

    if (this.endTime <= this.startTime) {
      throw new Error('End time must be after start time');
    }

    if (!Object.values(AuctionStatus).includes(this.status)) {
      throw new Error('Invalid auction status');
    }

    // Duration validation
    const durationHours = (this.endTime - this.startTime) / (1000 * 60 * 60);
    if (durationHours < AUCTION.MIN_DURATION_HOURS) {
      throw new Error(`Auction duration must be at least ${AUCTION.MIN_DURATION_HOURS} hours`);
    }

    const durationDays = durationHours / 24;
    if (durationDays > AUCTION.MAX_DURATION_DAYS) {
      throw new Error(`Auction duration cannot exceed ${AUCTION.MAX_DURATION_DAYS} days`);
    }
  }

  /**
   * Açık artırmanın aktif olup olmadığını kontrol et
   */
  isActive() {
    const now = new Date();
    return this.status === AuctionStatus.ACTIVE && 
           now >= this.startTime && 
           now <= this.endTime;
  }

  /**
   * Açık artırmanın başlamış olup olmadığını kontrol et
   */
  hasStarted() {
    return new Date() >= this.startTime;
  }

  /**
   * Açık artırmanın bitmiş olup olmadığını kontrol et
   */
  hasEnded() {
    return new Date() > this.endTime;
  }

  /**
   * Teklif verilebilir mi kontrol et
   */
  canPlaceBid() {
    return this.isActive() && !this.hasEnded();
  }

  /**
   * Minimum teklif miktarını hesapla
   */
  getMinimumBidAmount() {
    return this.currentPrice + this.minBidIncrement;
  }

  /**
   * Teklif ekle
   */
  placeBid(userId, amount, userName = null) {
    if (!this.canPlaceBid()) {
      throw new Error('Cannot place bid on inactive auction');
    }

    const minimumBid = this.getMinimumBidAmount();
    if (amount < minimumBid) {
      throw new Error(`Bid amount must be at least ${minimumBid}`);
    }

    // Buy now kontrolü
    if (this.buyNowPrice && amount >= this.buyNowPrice) {
      this.endAuctionWithBuyNow(userId, amount, userName);
      return;
    }

    const bid = {
      userId,
      amount,
      name: userName,
      timestamp: new Date(),
      _id: this.generateBidId()
    };

    this.bids.push(bid);
    this.currentPrice = amount;
    this.updatedAt = new Date();

    // Auto extend kontrolü
    if (this.autoExtend) {
      this.checkAutoExtend();
    }
  }

  /**
   * Buy now ile açık artırmayı bitir
   */
  endAuctionWithBuyNow(userId, amount, userName = null) {
    const bid = {
      userId,
      amount,
      name: userName,
      timestamp: new Date(),
      _id: this.generateBidId(),
      isBuyNow: true
    };

    this.bids.push(bid);
    this.currentPrice = amount;
    this.winnerId = userId;
    this.winningBid = bid;
    this.status = AuctionStatus.COMPLETED;
    this.endTime = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Otomatik uzatma kontrolü
   */
  checkAutoExtend() {
    const now = new Date();
    const timeLeft = this.endTime - now;
    const extendThreshold = this.extendMinutes * 60 * 1000; // milliseconds

    if (timeLeft <= extendThreshold) {
      this.endTime = new Date(now.getTime() + extendThreshold);
    }
  }

  /**
   * Açık artırmayı başlat
   */
  start() {
    if (this.status !== AuctionStatus.PENDING) {
      throw new Error('Only pending auctions can be started');
    }

    this.status = AuctionStatus.ACTIVE;
    this.updatedAt = new Date();
  }

  /**
   * Açık artırmayı bitir
   */
  end() {
    if (this.status !== AuctionStatus.ACTIVE) {
      throw new Error('Only active auctions can be ended');
    }

    this.status = AuctionStatus.COMPLETED;
    
    // Kazananı belirle
    if (this.bids.length > 0) {
      const winningBid = this.getHighestBid();
      this.winnerId = winningBid.userId;
      this.winningBid = winningBid;
    }

    this.updatedAt = new Date();
  }

  /**
   * Açık artırmayı iptal et
   */
  cancel() {
    if (this.status === AuctionStatus.COMPLETED) {
      throw new Error('Cannot cancel completed auction');
    }

    this.status = AuctionStatus.CANCELLED;
    this.updatedAt = new Date();
  }

  /**
   * En yüksek teklifi getir
   */
  getHighestBid() {
    if (this.bids.length === 0) {
      return null;
    }

    return this.bids.reduce((highest, current) => 
      current.amount > highest.amount ? current : highest
    );
  }

  /**
   * Kullanıcının tekliflerini getir
   */
  getUserBids(userId) {
    return this.bids.filter(bid => bid.userId === userId);
  }

  /**
   * Görüntülenme sayısını artır
   */
  incrementViewCount() {
    this.viewCount += 1;
    this.updatedAt = new Date();
  }

  /**
   * Takip eden sayısını artır
   */
  incrementWatcherCount() {
    this.watcherCount += 1;
    this.updatedAt = new Date();
  }

  /**
   * Takip eden sayısını azalt
   */
  decrementWatcherCount() {
    if (this.watcherCount > 0) {
      this.watcherCount -= 1;
      this.updatedAt = new Date();
    }
  }

  /**
   * Bid ID oluştur
   */
  generateBidId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Entity'yi plain object'e çevir
   */
  toJSON() {
    return {
      id: this.id,
      productId: this.productId,
      sellerId: this.sellerId,
      title: this.title,
      description: this.description,
      startingPrice: this.startingPrice,
      currentPrice: this.currentPrice,
      buyNowPrice: this.buyNowPrice,
      minBidIncrement: this.minBidIncrement,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status,
      bids: this.bids,
      winnerId: this.winnerId,
      winningBid: this.winningBid,
      autoExtend: this.autoExtend,
      extendMinutes: this.extendMinutes,
      viewCount: this.viewCount,
      watcherCount: this.watcherCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Entity'yi database için hazırla
   */
  toPersistence() {
    return {
      _id: this.id,
      productId: this.productId,
      sellerId: this.sellerId,
      title: this.title,
      description: this.description,
      startingPrice: this.startingPrice,
      currentPrice: this.currentPrice,
      buyNowPrice: this.buyNowPrice,
      minBidIncrement: this.minBidIncrement,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status,
      bids: this.bids,
      winnerId: this.winnerId,
      winningBid: this.winningBid,
      autoExtend: this.autoExtend,
      extendMinutes: this.extendMinutes,
      viewCount: this.viewCount,
      watcherCount: this.watcherCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Database'den entity oluştur
   */
  static fromPersistence(data) {
    return new Auction({
      id: data._id,
      productId: data.productId,
      sellerId: data.sellerId,
      title: data.title,
      description: data.description,
      startingPrice: data.startingPrice,
      currentPrice: data.currentPrice,
      buyNowPrice: data.buyNowPrice,
      minBidIncrement: data.minBidIncrement,
      startTime: data.startTime,
      endTime: data.endTime,
      status: data.status,
      bids: data.bids,
      winnerId: data.winnerId,
      winningBid: data.winningBid,
      autoExtend: data.autoExtend,
      extendMinutes: data.extendMinutes,
      viewCount: data.viewCount,
      watcherCount: data.watcherCount,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}

module.exports = Auction;
