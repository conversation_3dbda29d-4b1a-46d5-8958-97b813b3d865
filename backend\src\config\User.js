/**
 * User Entity (Domain Object)
 * <PERSON><PERSON> mantığından bağımsız kullanıcı varlığı
 */
import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

const { UserRole, UserStatus, ApprovalStatus } = require('../../shared/enums');
const { VALIDATION } = require('../../shared/constants');

class User {
  constructor({
    id = null,
    name,
    email,
    username,
    password = null, // Hash'lenmiş şifre
    role = UserRole.USER,
    status = UserStatus.ACTIVE,
    approvalStatus = ApprovalStatus.APPROVED,
    isEmailVerified = false,
    canBid = false,
    canParticipateInLottery = false,
    profile = {},
    createdAt = new Date(),
    updatedAt = new Date()
  }) {
    this.id = id;
    this.name = name;
    this.email = email;
    this.username = username;
    this.password = password;
    this.role = role;
    this.status = status;
    this.approvalStatus = approvalStatus;
    this.isEmailVerified = isEmailVerified;
    this.canBid = canBid;
    this.canParticipateInLottery = canParticipateInLottery;
    this.profile = profile;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;

    // Validation
    this.validate();
  }

  /**
   * Entity validation
   */
  validate() {
    if (!this.name || this.name.length < VALIDATION.NAME_MIN_LENGTH) {
      throw new Error(`Name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
    }

    if (!this.email || !this.isValidEmail(this.email)) {
      throw new Error('Valid email is required');
    }

    if (!this.username || this.username.length < VALIDATION.USERNAME_MIN_LENGTH) {
      throw new Error(`Username must be at least ${VALIDATION.USERNAME_MIN_LENGTH} characters`);
    }

    if (!Object.values(UserRole).includes(this.role)) {
      throw new Error('Invalid user role');
    }

    if (!Object.values(UserStatus).includes(this.status)) {
      throw new Error('Invalid user status');
    }
  }

  /**
   * Email validation
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Kullanıcının aktif olup olmadığını kontrol et
   */
  isActive() {
    return this.status === UserStatus.ACTIVE &&
           this.approvalStatus === ApprovalStatus.APPROVED;
  }

  /**
   * Kullanıcının admin olup olmadığını kontrol et
   */
  isAdmin() {
    return this.role === UserRole.ADMIN;
  }

  /**
   * Kullanıcının seller olup olmadığını kontrol et
   */
  isSeller() {
    return this.role === UserRole.SELLER;
  }

  /**
   * Kullanıcının teklif verebilip veremeyeceğini kontrol et
   */
  canMakeBid() {
    return this.isActive() && this.canBid;
  }

  /**
   * Kullanıcının çekilişe katılıp katılamayacağını kontrol et
   */
  canJoinLottery() {
    return this.isActive() && this.canParticipateInLottery;
  }

  /**
   * Kullanıcı bilgilerini güncelle
   */
  updateProfile(profileData) {
    this.profile = { ...this.profile, ...profileData };
    this.updatedAt = new Date();
  }

  /**
   * Email doğrulama durumunu güncelle
   */
  verifyEmail() {
    this.isEmailVerified = true;
    this.updatedAt = new Date();
  }

  /**
   * Kullanıcı durumunu güncelle
   */
  updateStatus(status) {
    if (!Object.values(UserStatus).includes(status)) {
      throw new Error('Invalid user status');
    }
    this.status = status;
    this.updatedAt = new Date();
  }

  /**
   * Onay durumunu güncelle
   */
  updateApprovalStatus(approvalStatus) {
    if (!Object.values(ApprovalStatus).includes(approvalStatus)) {
      throw new Error('Invalid approval status');
    }
    this.approvalStatus = approvalStatus;
    this.updatedAt = new Date();
  }

  /**
   * Teklif verme iznini güncelle
   */
  updateBidPermission(canBid) {
    this.canBid = Boolean(canBid);
    this.updatedAt = new Date();
  }

  /**
   * Çekiliş katılım iznini güncelle
   */
  updateLotteryPermission(canParticipateInLottery) {
    this.canParticipateInLottery = Boolean(canParticipateInLottery);
    this.updatedAt = new Date();
  }

  /**
   * Entity'yi plain object'e çevir (password hariç)
   */
  toJSON() {
    console.log('🔍 User.toJSON - this.id:', this.id);
    const { password, ...userWithoutPassword } = this;
    const result = {
      ...userWithoutPassword,
      _id: this.id // Frontend _id bekliyor
    };
    console.log('🔍 User.toJSON - result.id:', result.id);
    return result;
  }

  /**
   * Entity'yi database için hazırla
   */
  toPersistence() {
    const data = {
      name: this.name,
      email: this.email,
      username: this.username,
      password: this.password,
      role: this.role,
      status: this.status,
      approvalStatus: this.approvalStatus,
      isEmailVerified: this.isEmailVerified,
      canBid: this.canBid,
      canParticipateInLottery: this.canParticipateInLottery,
      profile: this.profile,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };

    // Only include _id if it exists (for updates)
    if (this.id) {
      data._id = this.id;
    }

    return data;
  }

  /**
   * Database'den entity oluştur
   */
  static fromPersistence(data) {
    return new User({
      id: data._id,
      name: data.name,
      email: data.email,
      username: data.username,
      password: data.password,
      role: data.role,
      status: data.status,
      approvalStatus: data.approvalStatus,
      isEmailVerified: data.isEmailVerified,
      canBid: data.canBid,
      canParticipateInLottery: data.canParticipateInLottery,
      profile: data.profile,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}

module.exports = User;
