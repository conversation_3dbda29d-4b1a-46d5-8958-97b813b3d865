import { Slot } from "expo-router";
import SafeScreen from "../components/SafeScreen";
import { ClerkProvider } from '@clerk/clerk-expo';
import { tokenCache } from '@clerk/clerk-expo/token-cache';
import { useApiAuth } from '../hooks/useApiAuth';
import { CLERK_PUBLISHABLE_KEY } from '../constants/env';

function AppContent() {
  useApiAuth(); // API token'ını güncelle

  return (
    <SafeScreen>
      <Slot />
    </SafeScreen>
  );
}

export default function Rootlayout() {
  return (
    <ClerkProvider
      publishableKey={CLERK_PUBLISHABLE_KEY}
      tokenCache={tokenCache}
    >
      <AppContent />
    </ClerkProvider>
  );
}
