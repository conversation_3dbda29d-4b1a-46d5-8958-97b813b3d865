import express from 'express';
import {
    getUserOrders,
    getOrderById,
    createOrder,
    updateOrderStatus,
    cancelOrder,
    getAllOrders
} from '../controllers/ordersController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All order routes require authentication
router.get('/', authenticateUser, getUserOrders);
router.get('/all', authenticateUser, getAllOrders);
router.get('/:id', authenticateUser, getOrderById);
router.post('/', authenticateUser, createOrder);
router.put('/:id/status', authenticateUser, updateOrderStatus);
router.put('/:id/cancel', authenticateUser, cancelOrder);

export default router;
