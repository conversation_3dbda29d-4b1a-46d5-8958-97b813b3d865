{"version": 3, "sources": ["../src/deprecated.ts", "../src/utils/runtimeEnvironment.ts"], "sourcesContent": ["import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n", "export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACWO,IAAM,oBAAoB,MAAe;AAC9C,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;AAEO,IAAM,0BAA0B,MAAe;AACpD,MAAI;AACF,WAAO,QAAQ,IAAI,aAAa;AAAA,EAElC,QAAQ;AAAA,EAAC;AAGT,SAAO;AACT;;;ADRA,IAAM,oBAAoB,oBAAI,IAAY;AACnC,IAAM,aAAa,CAAC,QAAgB,SAAiB,QAAuB;AACjF,QAAM,cAAc,kBAAkB,KAAK,wBAAwB;AACnE,QAAM,YAAY,OAAO;AACzB,MAAI,kBAAkB,IAAI,SAAS,KAAK,aAAa;AACnD;AAAA,EACF;AACA,oBAAkB,IAAI,SAAS;AAE/B,UAAQ;AAAA,IACN,iCAAiC,MAAM;AAAA,EAAmE,OAAO;AAAA,EACnH;AACF;AAyBO,IAAM,qBAAqB,CAAC,KAAe,UAAkB,SAAiB,WAAW,UAAgB;AAC9G,QAAM,SAAS,WAAW,MAAM,IAAI;AAEpC,MAAI,QAAQ,OAAO,QAAQ;AAC3B,SAAO,eAAe,QAAQ,UAAU;AAAA,IACtC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG,IAAI,IAAI,IAAI,QAAQ,EAAE;AACvD,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAY;AACd,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AAYO,IAAM,2BAA2B,CACtC,KACA,UACA,SACA,QACS;AACT,MAAI,QAAQ,IAAI,QAAQ;AACxB,SAAO,eAAe,KAAK,UAAU;AAAA,IACnC,MAAM;AACJ,iBAAW,UAAU,SAAS,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,IACA,IAAI,GAAY;AACd,cAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;", "names": []}