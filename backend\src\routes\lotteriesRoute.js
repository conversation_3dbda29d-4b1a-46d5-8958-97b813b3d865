import express from 'express';
import {
    getLotteries,
    getLotteryById,
    createLottery,
    buyTicket,
    drawLottery,
    getUserTickets
} from '../controllers/lotteriesController.js';
import { authenticateUser, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', optionalAuth, getLotteries);
router.get('/:id', getLotteryById);

// Protected routes (require authentication)
router.get('/tickets/my', authenticateUser, getUserTickets);
router.post('/', authenticateUser, createLottery);
router.post('/:id/buy', authenticateUser, buyTicket);
router.post('/:id/draw', authenticateUser, drawLottery);

export default router;
