{"version": 3, "sources": ["../src/workerTimers/workerTimers.worker.ts", "../src/workerTimers/createWorkerTimers.ts"], "sourcesContent": ["const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener(\"message\",r=>{const e=r.data;switch(e.type){case\"setTimeout\":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id}),delete workerToTabIds[e.id]},e.ms);break;case\"clearTimeout\":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case\"setInterval\":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case\"clearInterval\":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n", "import { noop } from '../utils/noop';\nimport type {\n  WorkerClearTimeout,\n  WorkerSetTimeout,\n  WorkerTimeoutCallback,\n  WorkerTimerEvent,\n  WorkerTimerId,\n  WorkerTimerResponseEvent,\n} from './workerTimers.types';\n// @ts-ignore\n// eslint-disable-next-line import/default\nimport pollerWorkerSource from './workerTimers.worker';\n\nconst createWebWorker = (source: string, opts: ConstructorParameters<typeof Worker>[1] = {}): Worker | null => {\n  if (typeof Worker === 'undefined') {\n    return null;\n  }\n\n  try {\n    const blob = new Blob([source], { type: 'application/javascript; charset=utf-8' });\n    const workerScript = globalThis.URL.createObjectURL(blob);\n    return new Worker(workerScript, opts);\n  } catch {\n    console.warn('Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP');\n    return null;\n  }\n};\n\nconst fallbackTimers = () => {\n  const setTimeout = globalThis.setTimeout.bind(globalThis) as WorkerSetTimeout;\n  const setInterval = globalThis.setInterval.bind(globalThis) as WorkerSetTimeout;\n  const clearTimeout = globalThis.clearTimeout.bind(globalThis) as WorkerClearTimeout;\n  const clearInterval = globalThis.clearInterval.bind(globalThis) as WorkerClearTimeout;\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup: noop };\n};\n\nexport const createWorkerTimers = () => {\n  let id = 0;\n  const generateId = () => id++;\n  const callbacks = new Map<WorkerTimerId, WorkerTimeoutCallback>();\n  const post = (w: Worker | null, p: WorkerTimerEvent) => w?.postMessage(p);\n  const handleMessage = (e: MessageEvent<WorkerTimerResponseEvent>) => {\n    callbacks.get(e.data.id)?.();\n  };\n\n  let worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n  worker?.addEventListener('message', handleMessage);\n\n  if (!worker) {\n    return fallbackTimers();\n  }\n\n  const init = () => {\n    if (!worker) {\n      worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n      worker?.addEventListener('message', handleMessage);\n    }\n  };\n\n  const cleanup = () => {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      callbacks.clear();\n    }\n  };\n\n  const setTimeout: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, () => {\n      cb();\n      callbacks.delete(id);\n    });\n    post(worker, { type: 'setTimeout', id, ms });\n    return id;\n  };\n\n  const setInterval: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, cb);\n    post(worker, { type: 'setInterval', id, ms });\n    return id;\n  };\n\n  const clearTimeout: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearTimeout', id });\n  };\n\n  const clearInterval: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearInterval', id });\n  };\n\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup };\n};\n"], "mappings": ";;;;;AAAA;;;ACaA,IAAM,kBAAkB,CAAC,QAAgB,OAAgD,CAAC,MAAqB;AAC7G,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAEA,MAAI;AACF,UAAM,OAAO,IAAI,KAAK,CAAC,MAAM,GAAG,EAAE,MAAM,wCAAwC,CAAC;AACjF,UAAM,eAAe,WAAW,IAAI,gBAAgB,IAAI;AACxD,WAAO,IAAI,OAAO,cAAc,IAAI;AAAA,EACtC,QAAQ;AACN,YAAQ,KAAK,sFAAsF;AACnG,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,MAAM;AAC3B,QAAM,aAAa,WAAW,WAAW,KAAK,UAAU;AACxD,QAAM,cAAc,WAAW,YAAY,KAAK,UAAU;AAC1D,QAAM,eAAe,WAAW,aAAa,KAAK,UAAU;AAC5D,QAAM,gBAAgB,WAAW,cAAc,KAAK,UAAU;AAC9D,SAAO,EAAE,YAAY,aAAa,cAAc,eAAe,SAAS,KAAK;AAC/E;AAEO,IAAM,qBAAqB,MAAM;AACtC,MAAI,KAAK;AACT,QAAM,aAAa,MAAM;AACzB,QAAM,YAAY,oBAAI,IAA0C;AAChE,QAAM,OAAO,CAAC,GAAkB,MAAwB,GAAG,YAAY,CAAC;AACxE,QAAM,gBAAgB,CAAC,MAA8C;AACnE,cAAU,IAAI,EAAE,KAAK,EAAE,IAAI;AAAA,EAC7B;AAEA,MAAI,SAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACzE,UAAQ,iBAAiB,WAAW,aAAa;AAEjD,MAAI,CAAC,QAAQ;AACX,WAAO,eAAe;AAAA,EACxB;AAEA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,QAAQ;AACX,eAAS,gBAAgB,6BAAoB,EAAE,MAAM,eAAe,CAAC;AACrE,cAAQ,iBAAiB,WAAW,aAAa;AAAA,IACnD;AAAA,EACF;AAEA,QAAM,UAAU,MAAM;AACpB,QAAI,QAAQ;AACV,aAAO,UAAU;AACjB,eAAS;AACT,gBAAU,MAAM;AAAA,IAClB;AAAA,EACF;AAEA,QAAM,aAA+B,CAAC,IAAI,OAAO;AAC/C,SAAK;AACL,UAAMA,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,MAAM;AACtB,SAAG;AACH,gBAAU,OAAOA,GAAE;AAAA,IACrB,CAAC;AACD,SAAK,QAAQ,EAAE,MAAM,cAAc,IAAAA,KAAI,GAAG,CAAC;AAC3C,WAAOA;AAAA,EACT;AAEA,QAAM,cAAgC,CAAC,IAAI,OAAO;AAChD,SAAK;AACL,UAAMA,MAAK,WAAW;AACtB,cAAU,IAAIA,KAAI,EAAE;AACpB,SAAK,QAAQ,EAAE,MAAM,eAAe,IAAAA,KAAI,GAAG,CAAC;AAC5C,WAAOA;AAAA,EACT;AAEA,QAAM,eAAmC,CAAAA,QAAM;AAC7C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,gBAAgB,IAAAA,IAAG,CAAC;AAAA,EAC3C;AAEA,QAAM,gBAAoC,CAAAA,QAAM;AAC9C,SAAK;AACL,cAAU,OAAOA,GAAE;AACnB,SAAK,QAAQ,EAAE,MAAM,iBAAiB,IAAAA,IAAG,CAAC;AAAA,EAC5C;AAEA,SAAO,EAAE,YAAY,aAAa,cAAc,eAAe,QAAQ;AACzE;", "names": ["id"]}