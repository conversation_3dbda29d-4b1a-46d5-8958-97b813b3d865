import express from 'express';
import {
    getUserNotifications,
    mark<PERSON>R<PERSON>,
    markAllAsRead,
    deleteNotification,
    createNotification,
    sendBulkNotifications,
    getNotificationStats
} from '../controllers/notificationsController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All notification routes require authentication
router.get('/', authenticateUser, getUserNotifications);
router.get('/stats', authenticateUser, getNotificationStats);
router.post('/', authenticateUser, createNotification);
router.post('/bulk', authenticateUser, sendBulkNotifications);
router.put('/:id/read', authenticateUser, markAsRead);
router.put('/read-all', authenticateUser, markAllAsRead);
router.delete('/:id', authenticateUser, deleteNotification);

export default router;
