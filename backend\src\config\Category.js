/**
 * Category Entity
 * Core business entity representing a product category
 */
import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);


const { ValidationException } = require('../exceptions');
const { CategoryStatus } = require('../../shared/enums');

class Category {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.slug = data.slug;
    this.description = data.description;
    this.shortDescription = data.shortDescription;
    this.parentId = data.parentId;
    this.level = data.level || 0;
    this.order = data.order || 0;
    this.image = data.image || {};
    this.icon = data.icon;
    this.color = data.color;
    this.status = data.status || CategoryStatus.ACTIVE;
    this.isActive = data.isActive !== undefined ? data.isActive : (this.status === CategoryStatus.ACTIVE); // Backward compatibility
    this.isFeatured = data.isFeatured || false;
    this.showInMenu = data.showInMenu !== undefined ? data.showInMenu : true;
    this.showInHomepage = data.showInHomepage || false;
    this.seoTitle = data.seoTitle;
    this.seoDescription = data.seoDescription;
    this.seoKeywords = data.seoKeywords || [];
    this.metadata = data.metadata || {
      productCount: 0,
      childrenCount: 0,
      totalProductCount: 0,
      lastProductAddedAt: null,
      createdBy: null,
      updatedBy: null
    };
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
  }

  /**
   * Create Category from persistence data
   */
  static fromPersistence(data) {
    return new Category({
      ...data,
      id: data._id || data.id
    });
  }

  /**
   * Convert to persistence format
   */
  toPersistence() {
    return {
      id: this.id,
      name: this.name,
      slug: this.slug,
      description: this.description,
      shortDescription: this.shortDescription,
      parentId: this.parentId,
      level: this.level,
      order: this.order,
      image: this.image,
      icon: this.icon,
      color: this.color,
      status: this.status,
      isActive: this.isActive,
      isFeatured: this.isFeatured,
      showInMenu: this.showInMenu,
      showInHomepage: this.showInHomepage,
      seoTitle: this.seoTitle,
      seoDescription: this.seoDescription,
      seoKeywords: this.seoKeywords,
      metadata: this.metadata,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Generate slug from name
   */
  generateSlug(name = null) {
    const text = name || this.name;
    if (!text) {
      throw new ValidationException('Name is required to generate slug');
    }

    return text
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  }

  /**
   * Set parent category
   */
  setParent(parentId, parentLevel = 0) {
    if (parentId === this.id) {
      throw new ValidationException('Category cannot be its own parent');
    }

    this.parentId = parentId;
    this.level = parentLevel + 1;

    // Validate hierarchy depth
    if (this.level > 5) {
      throw new ValidationException('Category hierarchy cannot exceed 5 levels');
    }
  }

  /**
   * Update metadata
   */
  updateMetadata(updates) {
    this.metadata = {
      ...this.metadata,
      ...updates,
      updatedAt: new Date()
    };
  }

  /**
   * Increment product count
   */
  incrementProductCount() {
    this.metadata.productCount += 1;
    this.metadata.totalProductCount += 1;
    this.metadata.lastProductAddedAt = new Date();
  }

  /**
   * Decrement product count
   */
  decrementProductCount() {
    if (this.metadata.productCount > 0) {
      this.metadata.productCount -= 1;
    }
    if (this.metadata.totalProductCount > 0) {
      this.metadata.totalProductCount -= 1;
    }
  }

  /**
   * Set product count
   */
  setProductCount(count) {
    if (count < 0) {
      throw new ValidationException('Product count cannot be negative');
    }
    this.metadata.productCount = count;
  }

  /**
   * Set children count
   */
  setChildrenCount(count) {
    if (count < 0) {
      throw new ValidationException('Children count cannot be negative');
    }
    this.metadata.childrenCount = count;
  }

  /**
   * Check if category is root (has no parent)
   */
  isRoot() {
    return !this.parentId;
  }

  /**
   * Check if category has children
   */
  hasChildren() {
    return this.metadata.childrenCount > 0;
  }

  /**
   * Check if category has products
   */
  hasProducts() {
    return this.metadata.productCount > 0;
  }

  /**
   * Check if category can be deleted
   */
  canBeDeleted() {
    if (this.hasChildren()) {
      return {
        canDelete: false,
        reason: 'Category has child categories'
      };
    }

    if (this.hasProducts()) {
      return {
        canDelete: false,
        reason: 'Category has products'
      };
    }

    return { canDelete: true };
  }

  /**
   * Get category URL
   */
  getUrl() {
    return `/category/${this.slug}`;
  }

  /**
   * Get category display name
   */
  getDisplayName() {
    return this.name;
  }

  /**
   * Get SEO title (fallback to name)
   */
  getSeoTitle() {
    return this.seoTitle || this.name;
  }

  /**
   * Get SEO description (fallback to description)
   */
  getSeoDescription() {
    return this.seoDescription || this.description || this.shortDescription;
  }

  /**
   * Activate category
   */
  activate() {
    this.status = CategoryStatus.ACTIVE;
    this.isActive = true;
  }

  /**
   * Deactivate category
   */
  deactivate() {
    this.status = CategoryStatus.INACTIVE;
    this.isActive = false;
  }

  /**
   * Feature category
   */
  feature() {
    this.isFeatured = true;
  }

  /**
   * Unfeature category
   */
  unfeature() {
    this.isFeatured = false;
  }

  /**
   * Show in menu
   */
  showInMenus() {
    this.showInMenu = true;
  }

  /**
   * Hide from menu
   */
  hideFromMenu() {
    this.showInMenu = false;
  }

  /**
   * Show on homepage
   */
  showOnHomepage() {
    this.showInHomepage = true;
  }

  /**
   * Hide from homepage
   */
  hideFromHomepage() {
    this.showInHomepage = false;
  }

  /**
   * Update SEO information
   */
  updateSeo(seoData) {
    if (seoData.seoTitle) {
      this.seoTitle = seoData.seoTitle;
    }
    if (seoData.seoDescription) {
      this.seoDescription = seoData.seoDescription;
    }
    if (seoData.seoKeywords) {
      this.seoKeywords = Array.isArray(seoData.seoKeywords)
        ? seoData.seoKeywords
        : [seoData.seoKeywords];
    }
  }

  /**
   * Validate category data
   */
  validate() {
    if (!this.name || this.name.trim().length === 0) {
      throw new ValidationException('Category name is required');
    }

    if (this.name.length > 100) {
      throw new ValidationException('Category name cannot exceed 100 characters');
    }

    if (!this.slug || this.slug.trim().length === 0) {
      this.slug = this.generateSlug();
    }

    if (this.description && this.description.length > 1000) {
      throw new ValidationException('Category description cannot exceed 1000 characters');
    }

    if (this.shortDescription && this.shortDescription.length > 200) {
      throw new ValidationException('Category short description cannot exceed 200 characters');
    }

    if (this.level < 0 || this.level > 5) {
      throw new ValidationException('Category level must be between 0 and 5');
    }

    if (this.order < 0) {
      throw new ValidationException('Category order cannot be negative');
    }

    if (this.seoTitle && this.seoTitle.length > 60) {
      throw new ValidationException('SEO title cannot exceed 60 characters');
    }

    if (this.seoDescription && this.seoDescription.length > 160) {
      throw new ValidationException('SEO description cannot exceed 160 characters');
    }

    if (this.color && !/^#[0-9A-F]{6}$/i.test(this.color)) {
      throw new ValidationException('Color must be a valid hex color code');
    }
  }

  /**
   * Get category summary
   */
  getSummary() {
    return {
      id: this.id,
      name: this.name,
      slug: this.slug,
      level: this.level,
      status: this.status,
      isActive: this.isActive,
      isFeatured: this.isFeatured,
      productCount: this.metadata.productCount,
      childrenCount: this.metadata.childrenCount,
      hasChildren: this.hasChildren(),
      hasProducts: this.hasProducts(),
      canBeDeleted: this.canBeDeleted().canDelete
    };
  }

  /**
   * Clone category
   */
  clone() {
    return new Category(this.toPersistence());
  }

  /**
   * Compare with another category
   */
  equals(other) {
    if (!(other instanceof Category)) {
      return false;
    }
    return this.id === other.id;
  }

  /**
   * Convert to JSON
   */
  toJSON() {
    return {
      ...this.toPersistence(),
      _id: this.id // Frontend _id bekliyor
    };
  }

  /**
   * Convert to string
   */
  toString() {
    return `Category(${this.id}: ${this.name})`;
  }
}

module.exports = Category;
