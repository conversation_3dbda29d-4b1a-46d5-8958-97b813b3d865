/**
 * Backend Service - Backend API ile iletişim için <PERSON> servis
 */

import apiService from './ApiService';
import { API_URL } from '../constants/api';

// API servisinin base URL'ini ayarla
apiService.setBaseUrl(API_URL);

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  images: string[];
  category: string;
  stock: number;
  featured: boolean;
  status: string;
  seller_id: string;
  specifications: Record<string, any>;
  tags: string[];
  dynamic_pricing: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  username: string;
  role: string;
  status: string;
  approval_status: string;
  is_email_verified: boolean;
  can_bid: boolean;
  can_participate_in_lottery: boolean;
  profile: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface Transaction {
  id: number;
  user_id: string;
  title: string;
  amount: number;
  category: string;
  created_at: string;
}

class BackendService {
  // Products API
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    featured?: boolean;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<{ products: Product[]; pagination: any }>(endpoint);
  }

  async getProductById(id: number) {
    return apiService.get<Product>(`/products/${id}`);
  }

  async createProduct(productData: Partial<Product>) {
    return apiService.post<Product>('/products', productData);
  }

  async updateProduct(id: number, productData: Partial<Product>) {
    return apiService.put<Product>(`/products/${id}`, productData);
  }

  async deleteProduct(id: number) {
    return apiService.delete(`/products/${id}`);
  }

  async getProductsBySeller(sellerId: string, params?: { page?: number; limit?: number }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/products/seller/${sellerId}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<Product[]>(endpoint);
  }

  // Users API
  async getCurrentUser() {
    return apiService.get<User>('/users/me');
  }

  async getUserProfile(userId: string) {
    return apiService.get<User>(`/users/${userId}`);
  }

  async updateUserProfile(userData: Partial<User>) {
    return apiService.put<User>('/users/me', userData);
  }

  async getAllUsers(params?: {
    page?: number;
    limit?: number;
    role?: string;
    status?: string;
  }) {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/users/all${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return apiService.get<{ users: User[]; pagination: any }>(endpoint);
  }

  async updateUserPermissions(userId: string, permissions: {
    can_bid?: boolean;
    can_participate_in_lottery?: boolean;
    role?: string;
    status?: string;
    approval_status?: string;
  }) {
    return apiService.put<User>(`/users/${userId}/permissions`, permissions);
  }

  // Transactions API
  async getTransactionsByUserId(userId: string) {
    return apiService.get<Transaction[]>(`/transactions/${userId}`);
  }

  async createTransaction(transactionData: {
    user_id: string;
    title: string;
    amount: number;
    category: string;
  }) {
    return apiService.post<Transaction>('/transactions', transactionData);
  }

  async updateTransaction(id: number, transactionData: Partial<Transaction>) {
    return apiService.put<Transaction>(`/transactions/${id}`, transactionData);
  }

  async deleteTransaction(id: number) {
    return apiService.delete(`/transactions/${id}`);
  }

  async getTransactionSummary(userId: string) {
    return apiService.get<{
      balance: number;
      income: number;
      expense: number;
    }>(`/transactions/summary/${userId}`);
  }

  // Health Check
  async healthCheck() {
    return apiService.get('/health');
  }
}

// Singleton instance
const backendService = new BackendService();

export default backendService;
export { BackendService };
export type { Product, User, Transaction };
