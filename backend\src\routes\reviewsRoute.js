import express from 'express';
import {
    getProductReviews,
    getUserReviews,
    createReview,
    updateReview,
    deleteReview,
    getReviewById,
    getAllReviews
} from '../controllers/reviewsController.js';
import { authenticateUser, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/product/:productId', getProductReviews);
router.get('/:id', getReviewById);

// Protected routes (require authentication)
router.get('/', authenticateUser, getUserReviews);
router.get('/all/admin', authenticateUser, getAllReviews);
router.post('/', authenticateUser, createReview);
router.put('/:id', authenticateUser, updateReview);
router.delete('/:id', authenticateUser, deleteReview);

export default router;
