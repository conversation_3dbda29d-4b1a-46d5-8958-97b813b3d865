{"version": 3, "sources": ["../src/oauth.ts"], "sourcesContent": ["import type { OAuth<PERSON>rovider, OAuthProviderData, OAuthStrategy } from '@clerk/types';\n\nexport const OAUTH_PROVIDERS: OAuthProviderData[] = [\n  {\n    provider: 'google',\n    strategy: 'oauth_google',\n    name: 'Google',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/google',\n  },\n  {\n    provider: 'discord',\n    strategy: 'oauth_discord',\n    name: 'Discord',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/discord',\n  },\n  {\n    provider: 'facebook',\n    strategy: 'oauth_facebook',\n    name: 'Facebook',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/facebook',\n  },\n  {\n    provider: 'twitch',\n    strategy: 'oauth_twitch',\n    name: 'Twitch',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/twitch',\n  },\n  {\n    provider: 'twitter',\n    strategy: 'oauth_twitter',\n    name: 'Twitter',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/twitter',\n  },\n  {\n    provider: 'microsoft',\n    strategy: 'oauth_microsoft',\n    name: 'Microsoft',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/microsoft',\n  },\n  {\n    provider: 'tiktok',\n    strategy: 'oauth_tiktok',\n    name: 'TikTok',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/tiktok',\n  },\n  {\n    provider: 'linkedin',\n    strategy: 'oauth_linkedin',\n    name: 'LinkedIn',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linkedin',\n  },\n  {\n    provider: 'linkedin_oidc',\n    strategy: 'oauth_linkedin_oidc',\n    name: 'LinkedIn',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linkedin-oidc',\n  },\n  {\n    provider: 'github',\n    strategy: 'oauth_github',\n    name: 'GitHub',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/github',\n  },\n  {\n    provider: 'gitlab',\n    strategy: 'oauth_gitlab',\n    name: 'GitLab',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/gitlab',\n  },\n  {\n    provider: 'dropbox',\n    strategy: 'oauth_dropbox',\n    name: 'Dropbox',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/dropbox',\n  },\n  {\n    provider: 'atlassian',\n    strategy: 'oauth_atlassian',\n    name: 'Atlassian',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/atlassian',\n  },\n  {\n    provider: 'bitbucket',\n    strategy: 'oauth_bitbucket',\n    name: 'Bitbucket',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/bitbucket',\n  },\n  {\n    provider: 'hubspot',\n    strategy: 'oauth_hubspot',\n    name: 'HubSpot',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/hubspot',\n  },\n  {\n    provider: 'notion',\n    strategy: 'oauth_notion',\n    name: 'Notion',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/notion',\n  },\n  {\n    provider: 'apple',\n    strategy: 'oauth_apple',\n    name: 'Apple',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/apple',\n  },\n  {\n    provider: 'line',\n    strategy: 'oauth_line',\n    name: 'LINE',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/line',\n  },\n  {\n    provider: 'instagram',\n    strategy: 'oauth_instagram',\n    name: 'Instagram',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/instagram',\n  },\n  {\n    provider: 'coinbase',\n    strategy: 'oauth_coinbase',\n    name: 'Coinbase',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/coinbase',\n  },\n  {\n    provider: 'spotify',\n    strategy: 'oauth_spotify',\n    name: 'Spotify',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/spotify',\n  },\n  {\n    provider: 'xero',\n    strategy: 'oauth_xero',\n    name: 'Xero',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/xero',\n  },\n  {\n    provider: 'box',\n    strategy: 'oauth_box',\n    name: 'Box',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/box',\n  },\n  {\n    provider: 'slack',\n    strategy: 'oauth_slack',\n    name: 'Slack',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/slack',\n  },\n  {\n    provider: 'linear',\n    strategy: 'oauth_linear',\n    name: 'Linear',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/linear',\n  },\n  {\n    provider: 'x',\n    strategy: 'oauth_x',\n    name: 'X / Twitter',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/x-twitter-v2',\n  },\n  {\n    provider: 'enstall',\n    strategy: 'oauth_enstall',\n    name: 'Enstall',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/enstall',\n  },\n  {\n    provider: 'huggingface',\n    strategy: 'oauth_huggingface',\n    name: 'Hugging Face',\n    docsUrl: 'https://clerk.com/docs/authentication/social-connections/huggingface',\n  },\n];\n\ninterface getOAuthProviderDataProps {\n  provider?: OAuthProvider;\n  strategy?: OAuthStrategy;\n}\n\nexport function getOAuthProviderData({\n  provider,\n  strategy,\n}: getOAuthProviderDataProps): OAuthProviderData | undefined | null {\n  if (provider) {\n    return OAUTH_PROVIDERS.find(oauth_provider => oauth_provider.provider == provider);\n  }\n\n  return OAUTH_PROVIDERS.find(oauth_provider => oauth_provider.strategy == strategy);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEO,IAAM,kBAAuC;AAAA,EACl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qBAAqB;AAAA,EACnC;AAAA,EACA;AACF,GAAoE;AAClE,MAAI,UAAU;AACZ,WAAO,gBAAgB,KAAK,oBAAkB,eAAe,YAAY,QAAQ;AAAA,EACnF;AAEA,SAAO,gBAAgB,KAAK,oBAAkB,eAAe,YAAY,QAAQ;AACnF;", "names": []}