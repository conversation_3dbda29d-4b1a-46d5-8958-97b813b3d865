{"version": 3, "sources": ["../src/webauthn.ts", "../src/browser.ts"], "sourcesContent": ["import { isValidBrowser } from './browser';\n\nfunction isWebAuthnSupported() {\n  return (\n    isValidBrowser() &&\n    // Check if `PublicKeyCredential` is a constructor\n    typeof window.PublicKeyCredential === 'function'\n  );\n}\n\nasync function isWebAuthnAutofillSupported(): Promise<boolean> {\n  try {\n    return isWebAuthnSupported() && (await window.PublicKeyCredential.isConditionalMediationAvailable());\n  } catch {\n    return false;\n  }\n}\n\nasync function isWebAuthnPlatformAuthenticatorSupported(): Promise<boolean> {\n  try {\n    return (\n      typeof window !== 'undefined' &&\n      (await window.PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable())\n    );\n  } catch {\n    return false;\n  }\n}\n\nexport { isWebAuthnPlatformAuthenticatorSupported, isWebAuthnAutofillSupported, isWebAuthnSupported };\n", "/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIO,SAAS,YAAqB;AACnC,SAAO,OAAO,WAAW;AAC3B;AAEA,IAAM,YAAY;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB,IAAI,OAAO,UAAU,KAAK,GAAG,GAAG,GAAG;AAOlD,SAAS,iBAAiB,WAA4B;AAC3D,SAAO,CAAC,YAAY,QAAQ,cAAc,KAAK,SAAS;AAC1D;AAMO,SAAS,iBAA0B;AACxC,QAAM,YAAY,UAAU,IAAI,QAAQ,YAAY;AACpD,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AACA,SAAO,CAAC,iBAAiB,WAAW,SAAS,KAAK,CAAC,WAAW;AAChE;;;ADzDA,SAAS,sBAAsB;AAC7B,SACE,eAAe;AAAA,EAEf,OAAO,OAAO,wBAAwB;AAE1C;AAEA,eAAe,8BAAgD;AAC7D,MAAI;AACF,WAAO,oBAAoB,KAAM,MAAM,OAAO,oBAAoB,gCAAgC;AAAA,EACpG,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AAEA,eAAe,2CAA6D;AAC1E,MAAI;AACF,WACE,OAAO,WAAW,eACjB,MAAM,OAAO,oBAAoB,8CAA8C;AAAA,EAEpF,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": []}