import express from 'express';
import {
    getUserProfile,
    updateUserProfile,
    getCurrentUser,
    updateUserPermissions,
    getAllUsers
} from '../controllers/usersController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// Protected routes (require authentication)
router.get('/me', authenticateUser, getCurrentUser);
router.put('/me', authenticateUser, updateUserProfile);
router.get('/all', authenticateUser, getAllUsers);
router.get('/:userId', getUserProfile);
router.put('/:userId/permissions', authenticateUser, updateUserPermissions);

export default router;
