import { sql } from "../config/db.js";

// Get all auctions
export async function getAuctions(req, res) {
    try {
        const { 
            page = 1, 
            limit = 10, 
            status = 'active',
            category,
            search,
            sortBy = 'end_time',
            sortOrder = 'ASC'
        } = req.query;

        const offset = (page - 1) * limit;
        
        let whereConditions = [];
        if (status) whereConditions.push(`a.status = '${status}'`);
        if (category) whereConditions.push(`p.category = '${category}'`);
        if (search) {
            whereConditions.push(`(a.title ILIKE '%${search}%' OR a.description ILIKE '%${search}%')`);
        }
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const auctions = await sql`
            SELECT a.*, p.name as product_name, p.images, p.category,
                   u.name as seller_name
            FROM auctions a
            JOIN products p ON a.product_id = p.id::text
            JOIN users u ON a.seller_id = u.id
            ${sql.unsafe(whereClause)}
            ORDER BY ${sql.unsafe(sortBy)} ${sql.unsafe(sortOrder)}
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM auctions a
            JOIN products p ON a.product_id = p.id::text
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            auctions,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting auctions:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get single auction
export async function getAuctionById(req, res) {
    try {
        const { id } = req.params;

        const auction = await sql`
            SELECT a.*, p.name as product_name, p.images, p.category, p.specifications,
                   u.name as seller_name, u.profile as seller_profile
            FROM auctions a
            JOIN products p ON a.product_id = p.id::text
            JOIN users u ON a.seller_id = u.id
            WHERE a.id = ${id}
        `;

        if (auction.length === 0) {
            return res.status(404).json({ message: "Auction not found" });
        }

        // Increment view count
        await sql`
            UPDATE auctions SET view_count = view_count + 1 
            WHERE id = ${id}
        `;

        res.status(200).json(auction[0]);
    } catch (error) {
        console.error('Error getting auction:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create auction
export async function createAuction(req, res) {
    try {
        const sellerId = req.userId;
        const {
            product_id,
            title,
            description,
            starting_price,
            buy_now_price,
            min_bid_increment,
            start_time,
            end_time,
            auto_extend = true,
            extend_minutes = 10
        } = req.body;

        if (!product_id || !title || !description || !starting_price || !end_time) {
            return res.status(400).json({ message: "Missing required fields" });
        }

        // Check if product exists and belongs to user
        const product = await sql`
            SELECT * FROM products 
            WHERE id = ${product_id} AND seller_id = ${sellerId} AND status = 'active'
        `;

        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found or unauthorized" });
        }

        // Check if product is already in an active auction
        const existingAuction = await sql`
            SELECT id FROM auctions 
            WHERE product_id = ${product_id} AND status IN ('active', 'pending')
        `;

        if (existingAuction.length > 0) {
            return res.status(400).json({ message: "Product is already in an active auction" });
        }

        const newAuction = await sql`
            INSERT INTO auctions (
                product_id, seller_id, title, description, starting_price,
                current_price, buy_now_price, min_bid_increment, start_time,
                end_time, status, bids, winner_id, winning_bid, auto_extend,
                extend_minutes
            ) VALUES (
                ${product_id}, ${sellerId}, ${title}, ${description}, ${starting_price},
                ${starting_price}, ${buy_now_price || 0}, ${min_bid_increment || 1},
                ${start_time || new Date()}, ${end_time}, 'pending', '[]', '', '{}',
                ${auto_extend}, ${extend_minutes}
            ) RETURNING *
        `;

        res.status(201).json(newAuction[0]);
    } catch (error) {
        console.error('Error creating auction:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Place bid
export async function placeBid(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;
        const { amount } = req.body;

        if (!amount || amount <= 0) {
            return res.status(400).json({ message: "Valid bid amount is required" });
        }

        // Get auction details
        const auction = await sql`
            SELECT * FROM auctions WHERE id = ${id}
        `;

        if (auction.length === 0) {
            return res.status(404).json({ message: "Auction not found" });
        }

        const auctionData = auction[0];

        // Check if auction is active
        if (auctionData.status !== 'active') {
            return res.status(400).json({ message: "Auction is not active" });
        }

        // Check if auction has ended
        if (new Date() > new Date(auctionData.end_time)) {
            return res.status(400).json({ message: "Auction has ended" });
        }

        // Check if user is the seller
        if (auctionData.seller_id === userId) {
            return res.status(400).json({ message: "Sellers cannot bid on their own auctions" });
        }

        // Check if bid amount is valid
        const minBidAmount = auctionData.current_price + auctionData.min_bid_increment;
        if (amount < minBidAmount) {
            return res.status(400).json({ 
                message: `Bid must be at least ${minBidAmount}` 
            });
        }

        // Check user permissions
        const user = await sql`
            SELECT can_bid FROM users WHERE id = ${userId}
        `;

        if (user.length === 0 || !user[0].can_bid) {
            return res.status(403).json({ message: "You are not allowed to bid" });
        }

        // Create bid record
        const newBid = await sql`
            INSERT INTO bids (auction_id, user_id, amount)
            VALUES (${id}, ${userId}, ${amount})
            RETURNING *
        `;

        // Update auction with new bid
        const bids = JSON.parse(auctionData.bids || '[]');
        bids.push({
            user_id: userId,
            amount: amount,
            timestamp: new Date()
        });

        let updateData = {
            current_price: amount,
            bids: JSON.stringify(bids)
        };

        // Check for auto-extend
        const timeLeft = new Date(auctionData.end_time) - new Date();
        if (auctionData.auto_extend && timeLeft < (auctionData.extend_minutes * 60 * 1000)) {
            const newEndTime = new Date(Date.now() + (auctionData.extend_minutes * 60 * 1000));
            updateData.end_time = newEndTime;
        }

        await sql`
            UPDATE auctions SET
                current_price = ${updateData.current_price},
                bids = ${updateData.bids},
                end_time = COALESCE(${updateData.end_time || null}, end_time),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
        `;

        res.status(201).json({
            bid: newBid[0],
            message: "Bid placed successfully"
        });
    } catch (error) {
        console.error('Error placing bid:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get auction bids
export async function getAuctionBids(req, res) {
    try {
        const { id } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const bids = await sql`
            SELECT b.*, u.name as user_name
            FROM bids b
            JOIN users u ON b.user_id = u.id
            WHERE b.auction_id = ${id}
            ORDER BY b.amount DESC, b.created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM bids WHERE auction_id = ${id}
        `;

        res.status(200).json({
            bids,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting auction bids:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// End auction (admin or seller)
export async function endAuction(req, res) {
    try {
        const { id } = req.params;
        const userId = req.userId;

        // Get auction and check permissions
        const auction = await sql`
            SELECT * FROM auctions WHERE id = ${id}
        `;

        if (auction.length === 0) {
            return res.status(404).json({ message: "Auction not found" });
        }

        const user = await sql`
            SELECT role FROM users WHERE id = ${userId}
        `;

        const isAdmin = user.length > 0 && user[0].role === 'admin';
        const isSeller = auction[0].seller_id === userId;

        if (!isAdmin && !isSeller) {
            return res.status(403).json({ message: "Unauthorized" });
        }

        // Get highest bid
        const highestBid = await sql`
            SELECT * FROM bids 
            WHERE auction_id = ${id}
            ORDER BY amount DESC
            LIMIT 1
        `;

        let updateData = {
            status: 'ended',
            winner_id: '',
            winning_bid: '{}'
        };

        if (highestBid.length > 0) {
            updateData.winner_id = highestBid[0].user_id;
            updateData.winning_bid = JSON.stringify(highestBid[0]);
        }

        const updatedAuction = await sql`
            UPDATE auctions SET
                status = ${updateData.status},
                winner_id = ${updateData.winner_id},
                winning_bid = ${updateData.winning_bid},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
            RETURNING *
        `;

        res.status(200).json(updatedAuction[0]);
    } catch (error) {
        console.error('Error ending auction:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
