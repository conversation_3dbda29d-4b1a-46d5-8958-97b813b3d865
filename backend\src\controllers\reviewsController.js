import { sql } from "../config/db.js";

// Get reviews for a product
export async function getProductReviews(req, res) {
    try {
        const { productId } = req.params;
        const { page = 1, limit = 10, rating } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = `WHERE r.product_id = '${productId}'`;
        if (rating) {
            whereClause += ` AND r.rating = ${rating}`;
        }

        const reviews = await sql`
            SELECT r.*, u.name as user_name, u.profile
            FROM reviews r
            JOIN users u ON r.user_id = u.id
            ${sql.unsafe(whereClause)}
            ORDER BY r.created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM reviews r
            ${sql.unsafe(whereClause)}
        `;

        // Get rating statistics
        const ratingStats = await sql`
            SELECT 
                rating,
                COUNT(*) as count
            FROM reviews 
            WHERE product_id = ${productId}
            GROUP BY rating
            ORDER BY rating DESC
        `;

        const avgRating = await sql`
            SELECT 
                ROUND(AVG(rating), 2) as average_rating,
                COUNT(*) as total_reviews
            FROM reviews 
            WHERE product_id = ${productId}
        `;

        res.status(200).json({
            reviews,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            },
            statistics: {
                average_rating: avgRating[0].average_rating || 0,
                total_reviews: parseInt(avgRating[0].total_reviews),
                rating_breakdown: ratingStats
            }
        });
    } catch (error) {
        console.error('Error getting product reviews:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get user's reviews
export async function getUserReviews(req, res) {
    try {
        const userId = req.userId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const reviews = await sql`
            SELECT r.*, p.name as product_name, p.images
            FROM reviews r
            JOIN products p ON r.product_id = p.id::text
            WHERE r.user_id = ${userId}
            ORDER BY r.created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM reviews 
            WHERE user_id = ${userId}
        `;

        res.status(200).json({
            reviews,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting user reviews:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create a review
export async function createReview(req, res) {
    try {
        const userId = req.userId;
        const { product_id, rating, comment } = req.body;

        if (!product_id || !rating || rating < 1 || rating > 5) {
            return res.status(400).json({ message: "Product ID and valid rating (1-5) are required" });
        }

        // Check if product exists
        const product = await sql`
            SELECT id FROM products WHERE id = ${product_id} AND status = 'active'
        `;

        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        // Check if user already reviewed this product
        const existingReview = await sql`
            SELECT id FROM reviews 
            WHERE user_id = ${userId} AND product_id = ${product_id}
        `;

        if (existingReview.length > 0) {
            return res.status(400).json({ message: "You have already reviewed this product" });
        }

        // TODO: Check if user actually purchased this product
        // This would require checking order items JSON structure

        const newReview = await sql`
            INSERT INTO reviews (user_id, product_id, rating, comment)
            VALUES (${userId}, ${product_id}, ${rating}, ${comment || ''})
            RETURNING *
        `;

        res.status(201).json(newReview[0]);
    } catch (error) {
        console.error('Error creating review:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update a review
export async function updateReview(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;
        const { rating, comment } = req.body;

        if (rating && (rating < 1 || rating > 5)) {
            return res.status(400).json({ message: "Rating must be between 1 and 5" });
        }

        const updatedReview = await sql`
            UPDATE reviews SET
                rating = COALESCE(${rating}, rating),
                comment = COALESCE(${comment}, comment),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        if (updatedReview.length === 0) {
            return res.status(404).json({ message: "Review not found or unauthorized" });
        }

        res.status(200).json(updatedReview[0]);
    } catch (error) {
        console.error('Error updating review:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Delete a review
export async function deleteReview(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;

        // Check if current user is admin or review owner
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${userId}
        `;

        const review = await sql`
            SELECT user_id FROM reviews WHERE id = ${id}
        `;

        if (review.length === 0) {
            return res.status(404).json({ message: "Review not found" });
        }

        const isAdmin = currentUser.length > 0 && currentUser[0].role === 'admin';
        const isOwner = review[0].user_id === userId;

        if (!isAdmin && !isOwner) {
            return res.status(403).json({ message: "Unauthorized" });
        }

        await sql`
            DELETE FROM reviews WHERE id = ${id}
        `;

        res.status(200).json({ message: "Review deleted successfully" });
    } catch (error) {
        console.error('Error deleting review:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get single review
export async function getReviewById(req, res) {
    try {
        const { id } = req.params;

        const review = await sql`
            SELECT r.*, u.name as user_name, u.profile, p.name as product_name
            FROM reviews r
            JOIN users u ON r.user_id = u.id
            JOIN products p ON r.product_id = p.id::text
            WHERE r.id = ${id}
        `;

        if (review.length === 0) {
            return res.status(404).json({ message: "Review not found" });
        }

        res.status(200).json(review[0]);
    } catch (error) {
        console.error('Error getting review:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get all reviews (admin only)
export async function getAllReviews(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const { page = 1, limit = 10, rating, product_id } = req.query;
        const offset = (page - 1) * limit;

        let whereConditions = [];
        if (rating) whereConditions.push(`r.rating = ${rating}`);
        if (product_id) whereConditions.push(`r.product_id = '${product_id}'`);
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const reviews = await sql`
            SELECT r.*, u.name as user_name, p.name as product_name
            FROM reviews r
            JOIN users u ON r.user_id = u.id
            JOIN products p ON r.product_id = p.id::text
            ${sql.unsafe(whereClause)}
            ORDER BY r.created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM reviews r
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            reviews,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting all reviews:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
