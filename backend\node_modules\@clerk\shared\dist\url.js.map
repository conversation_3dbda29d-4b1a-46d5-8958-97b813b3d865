{"version": 3, "sources": ["../src/url.ts", "../src/constants.ts", "../src/utils/instance.ts"], "sourcesContent": ["import { CURRENT_DEV_INSTANCE_SUFFIXES, LEGACY_DEV_INSTANCE_SUFFIXES } from './constants';\nimport { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, version?: string) => {\n  if (!version && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!version) {\n    return 'latest';\n  }\n\n  return version.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (frontendApi: string, { clerkJSVersion }: { clerkJSVersion?: string }) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, clerkJSVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n\n// Returns true for hosts such as:\n// * accounts.foo.bar-13.lcl.dev\n// * accounts.foo.bar-13.lclstage.dev\n// * accounts.foo.bar-13.dev.lclclerk.com\nexport function isLegacyDevAccountPortalOrigin(host: string): boolean {\n  return LEGACY_DEV_INSTANCE_SUFFIXES.some(legacyDevSuffix => {\n    return host.startsWith('accounts.') && host.endsWith(legacyDevSuffix);\n  });\n}\n\n// Returns true for hosts such as:\n// * foo-bar-13.accounts.dev\n// * foo-bar-13.accountsstage.dev\n// * foo-bar-13.accounts.lclclerk.com\n// But false for:\n// * foo-bar-13.clerk.accounts.lclclerk.com\nexport function isCurrentDevAccountPortalOrigin(host: string): boolean {\n  return CURRENT_DEV_INSTANCE_SUFFIXES.some(currentDevSuffix => {\n    return host.endsWith(currentDevSuffix) && !host.endsWith('.clerk' + currentDevSuffix);\n  });\n}\n\n/* Functions below are taken from https://github.com/unjs/ufo/blob/main/src/utils.ts. LICENSE: MIT */\n\nconst TRAILING_SLASH_RE = /\\/$|\\/\\?|\\/#/;\n\nexport function hasTrailingSlash(input = '', respectQueryAndFragment?: boolean): boolean {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/');\n  }\n  return TRAILING_SLASH_RE.test(input);\n}\n\nexport function withTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return input.endsWith('/') ? input : input + '/';\n  }\n  if (hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n    if (!path) {\n      return fragment;\n    }\n  }\n  const [s0, ...s] = path.split('?');\n  return s0 + '/' + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function withoutTrailingSlash(input = '', respectQueryAndFragment?: boolean): string {\n  if (!respectQueryAndFragment) {\n    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || '/';\n  }\n  if (!hasTrailingSlash(input, true)) {\n    return input || '/';\n  }\n  let path = input;\n  let fragment = '';\n  const fragmentIndex = input.indexOf('#');\n  if (fragmentIndex >= 0) {\n    path = input.slice(0, fragmentIndex);\n    fragment = input.slice(fragmentIndex);\n  }\n  const [s0, ...s] = path.split('?');\n  return (s0.slice(0, -1) || '/') + (s.length > 0 ? `?${s.join('?')}` : '') + fragment;\n}\n\nexport function hasLeadingSlash(input = ''): boolean {\n  return input.startsWith('/');\n}\n\nexport function withoutLeadingSlash(input = ''): string {\n  return (hasLeadingSlash(input) ? input.slice(1) : input) || '/';\n}\n\nexport function withLeadingSlash(input = ''): string {\n  return hasLeadingSlash(input) ? input : '/' + input;\n}\n\nexport function cleanDoubleSlashes(input = ''): string {\n  return input\n    .split('://')\n    .map(string_ => string_.replace(/\\/{2,}/g, '/'))\n    .join('://');\n}\n\nexport function isNonEmptyURL(url: string) {\n  return url && url !== '/';\n}\n\nconst JOIN_LEADING_SLASH_RE = /^\\.?\\//;\n\nexport function joinURL(base: string, ...input: string[]): string {\n  let url = base || '';\n\n  for (const segment of input.filter(url => isNonEmptyURL(url))) {\n    if (url) {\n      // TODO: Handle .. when joining\n      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, '');\n      url = withTrailingSlash(url) + _segment;\n    } else {\n      url = segment;\n    }\n  }\n\n  return url;\n}\n\n/* Code below is taken from https://github.com/vercel/next.js/blob/fe7ff3f468d7651a92865350bfd0f16ceba27db5/packages/next/src/shared/lib/utils.ts. LICENSE: MIT */\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url);\n", "export const LEGACY_DEV_INSTANCE_SUFFIXES = ['.lcl.dev', '.lclstage.dev', '.lclclerk.com'];\nexport const CURRENT_DEV_INSTANCE_SUFFIXES = ['.accounts.dev', '.accountsstage.dev', '.accounts.lclclerk.com'];\nexport const DEV_OR_STAGING_SUFFIXES = [\n  '.lcl.dev',\n  '.stg.dev',\n  '.lclstage.dev',\n  '.stgstage.dev',\n  '.dev.lclclerk.com',\n  '.stg.lclclerk.com',\n  '.accounts.lclclerk.com',\n  'accountsstage.dev',\n  'accounts.dev',\n];\nexport const LOCAL_ENV_SUFFIXES = ['.lcl.dev', 'lclstage.dev', '.lclclerk.com', '.accounts.lclclerk.com'];\nexport const STAGING_ENV_SUFFIXES = ['.accountsstage.dev'];\nexport const LOCAL_API_URL = 'https://api.lclclerk.com';\nexport const STAGING_API_URL = 'https://api.clerkstage.dev';\nexport const PROD_API_URL = 'https://api.clerk.com';\n\n/**\n * Returns the URL for a static image\n * using the new img.clerk.com service\n */\nexport function iconImageUrl(id: string, format: 'svg' | 'jpeg' = 'svg'): string {\n  return `https://img.clerk.com/static/${id}.${format}`;\n}\n", "/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,+BAA+B,CAAC,YAAY,iBAAiB,eAAe;AAClF,IAAM,gCAAgC,CAAC,iBAAiB,sBAAsB,wBAAwB;;;ACEtG,SAAS,UAAU,aAA8B;AACtD,SACE,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,eAAe,KACpC,YAAY,SAAS,iBAAiB,KACtC,YAAY,SAAS,oBAAoB;AAE7C;;;AFPO,SAAS,kBAAkB,cAAc,IAAqB;AACnE,MAAI,YAAY,WAAW,GAAG,GAAG;AAC/B,kBAAc,YAAY,MAAM,CAAC;AAAA,EACnC;AACA,SAAO,IAAI,gBAAgB,WAAW;AACxC;AAEO,SAAS,YAAY,MAAM,IAAY;AAC5C,UAAQ,OAAO,IAAI,QAAQ,YAAY,EAAE;AAC3C;AAEO,SAAS,eAAe,KAAyB;AACtD,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI,IAAI,MAAM,iBAAiB,GAAG;AAChC,YAAQ;AAAA,EACV,WAAW,IAAI,MAAM,kBAAkB,GAAG;AACxC,WAAO;AAAA,EACT,OAAO;AACL,YAAQ;AAAA,EACV;AAEA,QAAM,WAAW,IAAI,QAAQ,OAAO,EAAE;AACtC,SAAO,SAAS,QAAQ;AAC1B;AAQO,IAAM,8BAA8B,CAAC,aAAqB,YAAqB;AACpF,MAAI,CAAC,WAAW,UAAU,WAAW,GAAG;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK;AAClC;AAOO,IAAM,eAAe,CAAC,aAAqB,EAAE,eAAe,MAAmC;AACpG,QAAM,sBAAsB,YAAY,QAAQ,iBAAiB,EAAE;AACnE,QAAM,QAAQ,4BAA4B,aAAa,cAAc;AACrE,SAAO,WAAW,mBAAmB,wBAAwB,kBAAkB,KAAK;AACtF;AAMO,SAAS,+BAA+B,MAAuB;AACpE,SAAO,6BAA6B,KAAK,qBAAmB;AAC1D,WAAO,KAAK,WAAW,WAAW,KAAK,KAAK,SAAS,eAAe;AAAA,EACtE,CAAC;AACH;AAQO,SAAS,gCAAgC,MAAuB;AACrE,SAAO,8BAA8B,KAAK,sBAAoB;AAC5D,WAAO,KAAK,SAAS,gBAAgB,KAAK,CAAC,KAAK,SAAS,WAAW,gBAAgB;AAAA,EACtF,CAAC;AACH;AAIA,IAAM,oBAAoB;AAEnB,SAAS,iBAAiB,QAAQ,IAAI,yBAA4C;AACvF,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG;AAAA,EAC3B;AACA,SAAO,kBAAkB,KAAK,KAAK;AACrC;AAEO,SAAS,kBAAkB,QAAQ,IAAI,yBAA2C;AACvF,MAAI,CAAC,yBAAyB;AAC5B,WAAO,MAAM,SAAS,GAAG,IAAI,QAAQ,QAAQ;AAAA,EAC/C;AACA,MAAI,iBAAiB,OAAO,IAAI,GAAG;AACjC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AACpC,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AACjC,SAAO,KAAK,OAAO,EAAE,SAAS,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9D;AAEO,SAAS,qBAAqB,QAAQ,IAAI,yBAA2C;AAC1F,MAAI,CAAC,yBAAyB;AAC5B,YAAQ,iBAAiB,KAAK,IAAI,MAAM,MAAM,GAAG,EAAE,IAAI,UAAU;AAAA,EACnE;AACA,MAAI,CAAC,iBAAiB,OAAO,IAAI,GAAG;AAClC,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO;AACX,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM,QAAQ,GAAG;AACvC,MAAI,iBAAiB,GAAG;AACtB,WAAO,MAAM,MAAM,GAAG,aAAa;AACnC,eAAW,MAAM,MAAM,aAAa;AAAA,EACtC;AACA,QAAM,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,GAAG;AACjC,UAAQ,GAAG,MAAM,GAAG,EAAE,KAAK,QAAQ,EAAE,SAAS,IAAI,IAAI,EAAE,KAAK,GAAG,CAAC,KAAK,MAAM;AAC9E;AAEO,SAAS,gBAAgB,QAAQ,IAAa;AACnD,SAAO,MAAM,WAAW,GAAG;AAC7B;AAEO,SAAS,oBAAoB,QAAQ,IAAY;AACtD,UAAQ,gBAAgB,KAAK,IAAI,MAAM,MAAM,CAAC,IAAI,UAAU;AAC9D;AAEO,SAAS,iBAAiB,QAAQ,IAAY;AACnD,SAAO,gBAAgB,KAAK,IAAI,QAAQ,MAAM;AAChD;AAEO,SAAS,mBAAmB,QAAQ,IAAY;AACrD,SAAO,MACJ,MAAM,KAAK,EACX,IAAI,aAAW,QAAQ,QAAQ,WAAW,GAAG,CAAC,EAC9C,KAAK,KAAK;AACf;AAEO,SAAS,cAAc,KAAa;AACzC,SAAO,OAAO,QAAQ;AACxB;AAEA,IAAM,wBAAwB;AAEvB,SAAS,QAAQ,SAAiB,OAAyB;AAChE,MAAI,MAAM,QAAQ;AAElB,aAAW,WAAW,MAAM,OAAO,CAAAA,SAAO,cAAcA,IAAG,CAAC,GAAG;AAC7D,QAAI,KAAK;AAEP,YAAM,WAAW,QAAQ,QAAQ,uBAAuB,EAAE;AAC1D,YAAM,kBAAkB,GAAG,IAAI;AAAA,IACjC,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO;AACT;AAMA,IAAM,qBAAqB;AACpB,IAAM,gBAAgB,CAAC,QAAgB,mBAAmB,KAAK,GAAG;", "names": ["url"]}