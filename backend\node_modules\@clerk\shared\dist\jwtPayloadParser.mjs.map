{"version": 3, "sources": ["../src/jwtPayloadParser.ts"], "sourcesContent": ["import type {\n  JwtPayload,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n  SharedSignedInAuthObjectProperties,\n} from '@clerk/types';\n\nimport { splitByScope } from './authorization';\n\nexport const parsePermissions = ({ per, fpm }: { per?: string; fpm?: string }) => {\n  if (!per || !fpm) {\n    return { permissions: [], featurePermissionMap: [] };\n  }\n\n  const permissions = per.split(',').map(p => p.trim());\n\n  // TODO: make this more efficient\n  const featurePermissionMap = fpm\n    .split(',')\n    .map(permission => Number.parseInt(permission.trim(), 10))\n    .map((permission: number) =>\n      permission\n        .toString(2)\n        .padStart(permissions.length, '0')\n        .split('')\n        .map(bit => Number.parseInt(bit, 10))\n        .reverse(),\n    )\n    .filter(Boolean);\n\n  return { permissions, featurePermissionMap };\n};\n\nfunction buildOrgPermissions({\n  features,\n  permissions,\n  featurePermissionMap,\n}: {\n  features?: string[];\n  permissions?: string[];\n  featurePermissionMap?: number[][];\n}) {\n  // Early return if any required input is missing\n  if (!features || !permissions || !featurePermissionMap) {\n    return [];\n  }\n\n  const orgPermissions: string[] = [];\n\n  // Process each feature and its permissions in a single loop\n  for (let featureIndex = 0; featureIndex < features.length; featureIndex++) {\n    const feature = features[featureIndex];\n\n    if (featureIndex >= featurePermissionMap.length) {\n      continue;\n    }\n\n    const permissionBits = featurePermissionMap[featureIndex];\n    if (!permissionBits) continue;\n\n    for (let permIndex = 0; permIndex < permissionBits.length; permIndex++) {\n      if (permissionBits[permIndex] === 1) {\n        orgPermissions.push(`org:${feature}:${permissions[permIndex]}`);\n      }\n    }\n  }\n\n  return orgPermissions;\n}\n\n/**\n * @experimental\n *\n * Resolves the signed-in auth state from JWT claims.\n */\nconst __experimental_JWTPayloadToAuthObjectProperties = (claims: JwtPayload): SharedSignedInAuthObjectProperties => {\n  let orgId: string | undefined;\n  let orgRole: OrganizationCustomRoleKey | undefined;\n  let orgSlug: string | undefined;\n  let orgPermissions: OrganizationCustomPermissionKey[] | undefined;\n\n  // fva can be undefined for instances that have not opt-in\n  const factorVerificationAge = claims.fva ?? null;\n\n  // sts can be undefined for instances that have not opt-in\n  const sessionStatus = claims.sts ?? null;\n\n  switch (claims.v) {\n    case 2: {\n      if (claims.o) {\n        orgId = claims.o?.id;\n        orgSlug = claims.o?.slg;\n\n        if (claims.o?.rol) {\n          orgRole = `org:${claims.o?.rol}`;\n        }\n        const { org } = splitByScope(claims.fea);\n        const { permissions, featurePermissionMap } = parsePermissions({\n          per: claims.o?.per,\n          fpm: claims.o?.fpm,\n        });\n        orgPermissions = buildOrgPermissions({\n          features: org,\n          featurePermissionMap: featurePermissionMap,\n          permissions: permissions,\n        });\n      }\n      break;\n    }\n    default:\n      orgId = claims.org_id;\n      orgRole = claims.org_role;\n      orgSlug = claims.org_slug;\n      orgPermissions = claims.org_permissions;\n      break;\n  }\n\n  return {\n    sessionClaims: claims,\n    sessionId: claims.sid,\n    sessionStatus,\n    actor: claims.act,\n    userId: claims.sub,\n    orgId: orgId,\n    orgRole: orgRole,\n    orgSlug: orgSlug,\n    orgPermissions,\n    factorVerificationAge,\n  };\n};\n\nexport { __experimental_JWTPayloadToAuthObjectProperties };\n"], "mappings": ";;;;;;AASO,IAAM,mBAAmB,CAAC,EAAE,KAAK,IAAI,MAAsC;AAChF,MAAI,CAAC,OAAO,CAAC,KAAK;AAChB,WAAO,EAAE,aAAa,CAAC,GAAG,sBAAsB,CAAC,EAAE;AAAA,EACrD;AAEA,QAAM,cAAc,IAAI,MAAM,GAAG,EAAE,IAAI,OAAK,EAAE,KAAK,CAAC;AAGpD,QAAM,uBAAuB,IAC1B,MAAM,GAAG,EACT,IAAI,gBAAc,OAAO,SAAS,WAAW,KAAK,GAAG,EAAE,CAAC,EACxD;AAAA,IAAI,CAAC,eACJ,WACG,SAAS,CAAC,EACV,SAAS,YAAY,QAAQ,GAAG,EAChC,MAAM,EAAE,EACR,IAAI,SAAO,OAAO,SAAS,KAAK,EAAE,CAAC,EACnC,QAAQ;AAAA,EACb,EACC,OAAO,OAAO;AAEjB,SAAO,EAAE,aAAa,qBAAqB;AAC7C;AAEA,SAAS,oBAAoB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AACF,GAIG;AAED,MAAI,CAAC,YAAY,CAAC,eAAe,CAAC,sBAAsB;AACtD,WAAO,CAAC;AAAA,EACV;AAEA,QAAM,iBAA2B,CAAC;AAGlC,WAAS,eAAe,GAAG,eAAe,SAAS,QAAQ,gBAAgB;AACzE,UAAM,UAAU,SAAS,YAAY;AAErC,QAAI,gBAAgB,qBAAqB,QAAQ;AAC/C;AAAA,IACF;AAEA,UAAM,iBAAiB,qBAAqB,YAAY;AACxD,QAAI,CAAC,eAAgB;AAErB,aAAS,YAAY,GAAG,YAAY,eAAe,QAAQ,aAAa;AACtE,UAAI,eAAe,SAAS,MAAM,GAAG;AACnC,uBAAe,KAAK,OAAO,OAAO,IAAI,YAAY,SAAS,CAAC,EAAE;AAAA,MAChE;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAOA,IAAM,kDAAkD,CAAC,WAA2D;AAClH,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAGJ,QAAM,wBAAwB,OAAO,OAAO;AAG5C,QAAM,gBAAgB,OAAO,OAAO;AAEpC,UAAQ,OAAO,GAAG;AAAA,IAChB,KAAK,GAAG;AACN,UAAI,OAAO,GAAG;AACZ,gBAAQ,OAAO,GAAG;AAClB,kBAAU,OAAO,GAAG;AAEpB,YAAI,OAAO,GAAG,KAAK;AACjB,oBAAU,OAAO,OAAO,GAAG,GAAG;AAAA,QAChC;AACA,cAAM,EAAE,IAAI,IAAI,aAAa,OAAO,GAAG;AACvC,cAAM,EAAE,aAAa,qBAAqB,IAAI,iBAAiB;AAAA,UAC7D,KAAK,OAAO,GAAG;AAAA,UACf,KAAK,OAAO,GAAG;AAAA,QACjB,CAAC;AACD,yBAAiB,oBAAoB;AAAA,UACnC,UAAU;AAAA,UACV;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AACA;AAAA,IACF;AAAA,IACA;AACE,cAAQ,OAAO;AACf,gBAAU,OAAO;AACjB,gBAAU,OAAO;AACjB,uBAAiB,OAAO;AACxB;AAAA,EACJ;AAEA,SAAO;AAAA,IACL,eAAe;AAAA,IACf,WAAW,OAAO;AAAA,IAClB;AAAA,IACA,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;", "names": []}