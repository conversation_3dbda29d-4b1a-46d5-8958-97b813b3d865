import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Clerk<PERSON><PERSON>rrorJSO<PERSON> } from '@clerk/types';

declare function isUnauthorizedError(e: any): boolean;
declare function isCaptchaError(e: ClerkAPIResponseError): boolean;
declare function is4xxError(e: any): boolean;
declare function isNetworkError(e: any): boolean;
interface ClerkAPIResponseOptions {
    data: ClerkAPIErrorJSON[];
    status: number;
    clerkTraceId?: string;
    retryAfter?: number;
}
interface MetamaskError extends Error {
    code: 4001 | 32602 | 32603;
    message: string;
    data?: unknown;
}
declare function isKnownError(error: any): error is ClerkAPIResponseError | ClerkRuntimeError | MetamaskError;
declare function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError;
/**
 * Checks if the provided error object is an instance of ClerkRuntimeError.
 *
 * @param {any} err - The error object to check.
 * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.
 *
 * @example
 * const error = new ClerkRuntimeError('An error occurred');
 * if (isClerkRuntimeError(error)) {
 *   // Handle ClerkRuntimeError
 *   console.error('ClerkRuntimeError:', error.message);
 * } else {
 *   // Handle other errors
 *   console.error('Other error:', error.message);
 * }
 */
declare function isClerkRuntimeError(err: any): err is ClerkRuntimeError;
declare function isReverificationCancelledError(err: any): boolean;
declare function isMetamaskError(err: any): err is MetamaskError;
declare function isUserLockedError(err: any): boolean;
declare function isPasswordPwnedError(err: any): boolean;
declare function parseErrors(data?: ClerkAPIErrorJSON[]): ClerkAPIError[];
declare function parseError(error: ClerkAPIErrorJSON): ClerkAPIError;
declare function errorToJSON(error: ClerkAPIError | null): ClerkAPIErrorJSON;
declare class ClerkAPIResponseError extends Error {
    clerkError: true;
    status: number;
    message: string;
    clerkTraceId?: string;
    retryAfter?: number;
    errors: ClerkAPIError[];
    constructor(message: string, { data, status, clerkTraceId, retryAfter }: ClerkAPIResponseOptions);
    toString: () => string;
}
/**
 * Custom error class for representing Clerk runtime errors.
 *
 * @class ClerkRuntimeError
 * @example
 *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });
 */
declare class ClerkRuntimeError extends Error {
    clerkRuntimeError: true;
    /**
     * The error message.
     *
     * @type {string}
     */
    message: string;
    /**
     * A unique code identifying the error, can be used for localization.
     *
     * @type {string}
     */
    code: string;
    constructor(message: string, { code }: {
        code: string;
    });
    /**
     * Returns a string representation of the error.
     *
     * @returns {string} A formatted string with the error name and message.
     */
    toString: () => string;
}
declare class EmailLinkError extends Error {
    code: string;
    constructor(code: string);
}
declare function isEmailLinkError(err: Error): err is EmailLinkError;
/**
 * @deprecated Use `EmailLinkErrorCodeStatus` instead.
 *
 * @hidden
 */
declare const EmailLinkErrorCode: {
    Expired: string;
    Failed: string;
    ClientMismatch: string;
};
declare const EmailLinkErrorCodeStatus: {
    readonly Expired: "expired";
    readonly Failed: "failed";
    readonly ClientMismatch: "client_mismatch";
};
declare const DefaultMessages: Readonly<{
    InvalidProxyUrlErrorMessage: "The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})";
    InvalidPublishableKeyErrorMessage: "The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})";
    MissingPublishableKeyErrorMessage: "Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.";
    MissingSecretKeyErrorMessage: "Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.";
    MissingClerkProvider: "{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider";
}>;
type MessageKeys = keyof typeof DefaultMessages;
type Messages = Record<MessageKeys, string>;
type CustomMessages = Partial<Messages>;
type ErrorThrowerOptions = {
    packageName: string;
    customMessages?: CustomMessages;
};
interface ErrorThrower {
    setPackageName(options: ErrorThrowerOptions): ErrorThrower;
    setMessages(options: ErrorThrowerOptions): ErrorThrower;
    throwInvalidPublishableKeyError(params: {
        key?: string;
    }): never;
    throwInvalidProxyUrl(params: {
        url?: string;
    }): never;
    throwMissingPublishableKeyError(): never;
    throwMissingSecretKeyError(): never;
    throwMissingClerkProviderError(params: {
        source?: string;
    }): never;
    throw(message: string): never;
}
declare function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower;
type ClerkWebAuthnErrorCode = 'passkey_not_supported' | 'passkey_pa_not_supported' | 'passkey_invalid_rpID_or_domain' | 'passkey_already_exists' | 'passkey_operation_aborted' | 'passkey_retrieval_cancelled' | 'passkey_retrieval_failed' | 'passkey_registration_cancelled' | 'passkey_registration_failed';
declare class ClerkWebAuthnError extends ClerkRuntimeError {
    /**
     * A unique code identifying the error, can be used for localization.
     */
    code: ClerkWebAuthnErrorCode;
    constructor(message: string, { code }: {
        code: ClerkWebAuthnErrorCode;
    });
}

export { ClerkAPIResponseError, ClerkRuntimeError, ClerkWebAuthnError, EmailLinkError, EmailLinkErrorCode, EmailLinkErrorCodeStatus, type ErrorThrower, type ErrorThrowerOptions, type MetamaskError, buildErrorThrower, errorToJSON, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMetamaskError, isNetworkError, isPasswordPwnedError, isReverificationCancelledError, isUnauthorizedError, isUserLockedError, parseError, parseErrors };
