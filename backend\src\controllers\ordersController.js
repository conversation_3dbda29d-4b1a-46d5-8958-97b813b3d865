import { sql } from "../config/db.js";

// Get user's orders
export async function getUserOrders(req, res) {
    try {
        const userId = req.userId;
        const { page = 1, limit = 10, status } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = `WHERE user_id = '${userId}'`;
        if (status) {
            whereClause += ` AND status = '${status}'`;
        }

        const orders = await sql`
            SELECT * FROM orders 
            ${sql.unsafe(whereClause)}
            ORDER BY created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM orders 
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            orders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting user orders:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get single order
export async function getOrderById(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;

        const order = await sql`
            SELECT * FROM orders 
            WHERE id = ${id} AND user_id = ${userId}
        `;

        if (order.length === 0) {
            return res.status(404).json({ message: "Order not found" });
        }

        res.status(200).json(order[0]);
    } catch (error) {
        console.error('Error getting order:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create new order
export async function createOrder(req, res) {
    try {
        const userId = req.userId;
        const {
            items,
            shipping_address,
            billing_address,
            payment_method,
            notes = '',
            shipping_cost = 0,
            tax_amount = 0,
            discount_amount = 0,
            coupon_code = ''
        } = req.body;

        if (!items || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json({ message: "Items are required" });
        }

        if (!shipping_address || !billing_address || !payment_method) {
            return res.status(400).json({ message: "Address and payment method are required" });
        }

        // Calculate total amount
        let total_amount = 0;
        for (const item of items) {
            total_amount += item.price * item.quantity;
        }
        total_amount += shipping_cost + tax_amount - discount_amount;

        // Generate order number
        const order_number = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        const newOrder = await sql`
            INSERT INTO orders (
                user_id, items, total_amount, shipping_address, billing_address,
                payment_method, payment_status, status, order_number, notes,
                shipping_cost, tax_amount, discount_amount, coupon_code,
                estimated_delivery
            ) VALUES (
                ${userId}, ${JSON.stringify(items)}, ${total_amount}, 
                ${JSON.stringify(shipping_address)}, ${JSON.stringify(billing_address)},
                ${payment_method}, 'pending', 'pending', ${order_number}, ${notes},
                ${shipping_cost}, ${tax_amount}, ${discount_amount}, ${coupon_code},
                ${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)} -- 7 days from now
            ) RETURNING *
        `;

        res.status(201).json(newOrder[0]);
    } catch (error) {
        console.error('Error creating order:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update order status
export async function updateOrderStatus(req, res) {
    try {
        const { id } = req.params;
        const { status, payment_status, tracking_number, notes } = req.body;

        // Check if current user is admin or order owner
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        const order = await sql`
            SELECT user_id FROM orders WHERE id = ${id}
        `;

        if (order.length === 0) {
            return res.status(404).json({ message: "Order not found" });
        }

        const isAdmin = currentUser.length > 0 && currentUser[0].role === 'admin';
        const isOwner = order[0].user_id === req.userId;

        if (!isAdmin && !isOwner) {
            return res.status(403).json({ message: "Unauthorized" });
        }

        // Only admin can update certain fields
        let updateFields = {};
        if (isAdmin) {
            if (status) updateFields.status = status;
            if (payment_status) updateFields.payment_status = payment_status;
            if (tracking_number) updateFields.tracking_number = tracking_number;
            if (notes) updateFields.notes = notes;
            
            if (status === 'delivered') {
                updateFields.delivered_at = new Date();
            }
            if (status === 'cancelled') {
                updateFields.cancelled_at = new Date();
            }
        }

        if (Object.keys(updateFields).length === 0) {
            return res.status(400).json({ message: "No valid fields to update" });
        }

        const updatedOrder = await sql`
            UPDATE orders SET
                status = COALESCE(${updateFields.status || null}, status),
                payment_status = COALESCE(${updateFields.payment_status || null}, payment_status),
                tracking_number = COALESCE(${updateFields.tracking_number || null}, tracking_number),
                notes = COALESCE(${updateFields.notes || null}, notes),
                delivered_at = COALESCE(${updateFields.delivered_at || null}, delivered_at),
                cancelled_at = COALESCE(${updateFields.cancelled_at || null}, cancelled_at),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
            RETURNING *
        `;

        res.status(200).json(updatedOrder[0]);
    } catch (error) {
        console.error('Error updating order:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Cancel order
export async function cancelOrder(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;
        const { cancellation_reason } = req.body;

        const order = await sql`
            SELECT * FROM orders 
            WHERE id = ${id} AND user_id = ${userId}
        `;

        if (order.length === 0) {
            return res.status(404).json({ message: "Order not found" });
        }

        if (order[0].status === 'delivered' || order[0].status === 'cancelled') {
            return res.status(400).json({ message: "Cannot cancel this order" });
        }

        const updatedOrder = await sql`
            UPDATE orders SET
                status = 'cancelled',
                cancelled_at = CURRENT_TIMESTAMP,
                cancellation_reason = ${cancellation_reason || 'Cancelled by user'},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        res.status(200).json(updatedOrder[0]);
    } catch (error) {
        console.error('Error cancelling order:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get all orders (admin only)
export async function getAllOrders(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const { page = 1, limit = 10, status, payment_status } = req.query;
        const offset = (page - 1) * limit;

        let whereConditions = [];
        if (status) whereConditions.push(`status = '${status}'`);
        if (payment_status) whereConditions.push(`payment_status = '${payment_status}'`);
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const orders = await sql`
            SELECT * FROM orders 
            ${sql.unsafe(whereClause)}
            ORDER BY created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM orders 
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            orders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting all orders:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
