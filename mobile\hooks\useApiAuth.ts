/**
 * API Authentication Hook
 * Clerk token'ını API servisine aktarır
 */

import { useEffect } from 'react';
import { useAuth } from '@clerk/clerk-expo';
import apiService from '../services/ApiService';

export const useApiAuth = () => {
  const { getToken, isSignedIn } = useAuth();

  useEffect(() => {
    const updateApiToken = async () => {
      if (isSignedIn) {
        try {
          const token = await getToken();
          apiService.setAuthToken(token);
        } catch (error) {
          console.warn('API token güncellenirken hata:', error);
          apiService.setAuthToken(null);
        }
      } else {
        apiService.setAuthToken(null);
      }
    };

    updateApiToken();
  }, [isSignedIn, getToken]);

  return { isSignedIn };
};
