import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

export async function initDB() {
    try {
    await sql`CREATE TABLE IF NOT EXISTS transactions(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            category VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE

        
    )`
    await sql`CREATE TABLE IF NOT EXISTS products(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            price DECIMAL NOT NULL,
            original_price DECIMAL,
            discount_percentage DECIMAL,
            images JSONB NOT NULL,
            category VARCHAR(255) NOT NULL,
            stock INT NOT NULL,
            featured BOOLEAN NOT NULL DEFAULT FALSE,
            status VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            specifications JSONB NOT NULL,
            tags J<PERSON>NB NOT NULL,
            dynamic_pricing JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

    )`
     await sql`CREATE TABLE IF NOT EXISTS users(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            username VARCHAR(255) NOT NULL,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(255) NOT NULL,
            status VARCHAR(255) NOT NULL,
            approval_status VARCHAR(255) NOT NULL,
            is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
            can_bid BOOLEAN NOT NULL DEFAULT FALSE,
            can_participate_in_lottery BOOLEAN NOT NULL DEFAULT FALSE,
            profile JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS auctions(
            id SERIAL PRIMARY KEY,
            product_id VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            starting_price DECIMAL NOT NULL,
            current_price DECIMAL NOT NULL,
            buy_now_price DECIMAL NOT NULL,
            min_bid_increment DECIMAL NOT NULL,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE NOT NULL,
            status VARCHAR(255) NOT NULL,
            bids JSONB NOT NULL,
            winner_id VARCHAR(255) NOT NULL,
            winning_bid JSONB NOT NULL,
            auto_extend BOOLEAN NOT NULL DEFAULT TRUE,
            extend_minutes INT NOT NULL,
            view_count INT NOT NULL DEFAULT 0,
            watcher_count INT NOT NULL DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS orders(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            items JSONB NOT NULL,
            total_amount DECIMAL NOT NULL,
            shipping_address JSONB NOT NULL,
            billing_address JSONB NOT NULL,
            payment_method VARCHAR(255) NOT NULL,
            payment_status VARCHAR(255) NOT NULL,
            status VARCHAR(255) NOT NULL,
            order_number VARCHAR(255) NOT NULL,
            notes TEXT NOT NULL,
            shipping_cost DECIMAL NOT NULL,
            tax_amount DECIMAL NOT NULL,
            discount_amount DECIMAL NOT NULL,
            coupon_code VARCHAR(255) NOT NULL,
            tracking_number VARCHAR(255) NOT NULL,
            estimated_delivery TIMESTAMP WITH TIME ZONE NOT NULL,
            delivered_at TIMESTAMP WITH TIME ZONE NOT NULL,
            cancelled_at TIMESTAMP WITH TIME ZONE NOT NULL,
            cancellation_reason TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS lotteries(
            id SERIAL PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            ticket_price DECIMAL NOT NULL,
            max_tickets INT NOT NULL,
            numbers JSONB NOT NULL,
            tickets JSONB NOT NULL,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE NOT NULL,
            status VARCHAR(255) NOT NULL,
            winner_id VARCHAR(255) NOT NULL,
            winning_number INT NOT NULL,
            winner_username VARCHAR(255) NOT NULL,
            draw_time TIMESTAMP WITH TIME ZONE NOT NULL,
            total_prize DECIMAL NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS bids(
            id SERIAL PRIMARY KEY,
            auction_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS tickets(
            id SERIAL PRIMARY KEY,
            lottery_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255) NOT NULL,
            number INT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS notifications(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS reviews(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            rating INT NOT NULL,
            comment TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS watchlist(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS cart(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            quantity INT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS wishlists(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS addresses(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            address TEXT NOT NULL,
            city VARCHAR(255) NOT NULL,
            state VARCHAR(255) NOT NULL,
            zip VARCHAR(255) NOT NULL,
            country VARCHAR(255) NOT NULL,
            is_default BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS payment_methods(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            card_number VARCHAR(255) NOT NULL,
            expiration_date VARCHAR(255) NOT NULL,
            cvv VARCHAR(255) NOT NULL,
            is_default BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS categories(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS reports(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            reason VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS faqs(
            id SERIAL PRIMARY KEY,
            question VARCHAR(255) NOT NULL,
            answer TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS settings(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            value VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS analytics(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            value VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS notifications(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
     await sql`CREATE TABLE IF NOT EXISTS reviews(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            product_id VARCHAR(255) NOT NULL,
            rating INT NOT NULL,
            comment TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP

     )`
            console.log('Database initialized successfully');
    } catch (error) {
        console.log('Error initializing DB', error);
        process.exit(1);//status code 1 means falure 0 means success 
    }
}

