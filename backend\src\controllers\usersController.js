import { sql } from "../config/db.js";
import { clerkClient } from '@clerk/backend';

// Get user profile
export async function getUserProfile(req, res) {
    try {
        const { userId } = req.params;
        
        // Get user from database
        let user = await sql`
            SELECT * FROM users WHERE id = ${userId}
        `;

        // If user doesn't exist in our database, create from Clerk data
        if (user.length === 0) {
            try {
                const clerkUser = await clerkClient.users.getUser(userId);
                
                const newUser = await sql`
                    INSERT INTO users (
                        id, name, email, username, role, status, 
                        approval_status, is_email_verified, profile
                    ) VALUES (
                        ${userId},
                        ${clerkUser.firstName + ' ' + (clerkUser.lastName || '')},
                        ${clerkUser.emailAddresses[0]?.emailAddress || ''},
                        ${clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress || ''},
                        'user',
                        'active',
                        'approved',
                        ${clerkUser.emailAddresses[0]?.verification?.status === 'verified'},
                        ${JSON.stringify({
                            firstName: clerkUser.firstName,
                            lastName: clerkUser.lastName,
                            imageUrl: clerkUser.imageUrl,
                            createdAt: clerkUser.createdAt
                        })}
                    ) RETURNING *
                `;
                
                user = newUser;
            } catch (clerkError) {
                console.error('Error fetching user from Clerk:', clerkError);
                return res.status(404).json({ message: "User not found" });
            }
        }

        // Remove sensitive data
        const { password, ...userProfile } = user[0];
        res.status(200).json(userProfile);
    } catch (error) {
        console.error('Error getting user profile:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update user profile
export async function updateUserProfile(req, res) {
    try {
        const userId = req.userId; // From auth middleware
        const {
            name,
            username,
            profile
        } = req.body;

        // Check if username is already taken (if provided)
        if (username) {
            const existingUser = await sql`
                SELECT id FROM users WHERE username = ${username} AND id != ${userId}
            `;
            
            if (existingUser.length > 0) {
                return res.status(400).json({ message: "Username already taken" });
            }
        }

        const updatedUser = await sql`
            UPDATE users SET
                name = COALESCE(${name}, name),
                username = COALESCE(${username}, username),
                profile = COALESCE(${profile ? JSON.stringify(profile) : null}, profile),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${userId}
            RETURNING *
        `;

        if (updatedUser.length === 0) {
            return res.status(404).json({ message: "User not found" });
        }

        // Remove sensitive data
        const { password, ...userProfile } = updatedUser[0];
        res.status(200).json(userProfile);
    } catch (error) {
        console.error('Error updating user profile:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get current user (from token)
export async function getCurrentUser(req, res) {
    try {
        const userId = req.userId;
        
        let user = await sql`
            SELECT * FROM users WHERE id = ${userId}
        `;

        // If user doesn't exist in our database, create from Clerk data
        if (user.length === 0) {
            try {
                const clerkUser = await clerkClient.users.getUser(userId);
                
                const newUser = await sql`
                    INSERT INTO users (
                        id, name, email, username, role, status, 
                        approval_status, is_email_verified, profile
                    ) VALUES (
                        ${userId},
                        ${clerkUser.firstName + ' ' + (clerkUser.lastName || '')},
                        ${clerkUser.emailAddresses[0]?.emailAddress || ''},
                        ${clerkUser.username || clerkUser.emailAddresses[0]?.emailAddress || ''},
                        'user',
                        'active',
                        'approved',
                        ${clerkUser.emailAddresses[0]?.verification?.status === 'verified'},
                        ${JSON.stringify({
                            firstName: clerkUser.firstName,
                            lastName: clerkUser.lastName,
                            imageUrl: clerkUser.imageUrl,
                            createdAt: clerkUser.createdAt
                        })}
                    ) RETURNING *
                `;
                
                user = newUser;
            } catch (clerkError) {
                console.error('Error fetching user from Clerk:', clerkError);
                return res.status(404).json({ message: "User not found" });
            }
        }

        // Remove sensitive data
        const { password, ...userProfile } = user[0];
        res.status(200).json(userProfile);
    } catch (error) {
        console.error('Error getting current user:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update user permissions (admin only)
export async function updateUserPermissions(req, res) {
    try {
        const { userId } = req.params;
        const {
            can_bid,
            can_participate_in_lottery,
            role,
            status,
            approval_status
        } = req.body;

        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const updatedUser = await sql`
            UPDATE users SET
                can_bid = COALESCE(${can_bid}, can_bid),
                can_participate_in_lottery = COALESCE(${can_participate_in_lottery}, can_participate_in_lottery),
                role = COALESCE(${role}, role),
                status = COALESCE(${status}, status),
                approval_status = COALESCE(${approval_status}, approval_status),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${userId}
            RETURNING *
        `;

        if (updatedUser.length === 0) {
            return res.status(404).json({ message: "User not found" });
        }

        // Remove sensitive data
        const { password, ...userProfile } = updatedUser[0];
        res.status(200).json(userProfile);
    } catch (error) {
        console.error('Error updating user permissions:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get all users (admin only)
export async function getAllUsers(req, res) {
    try {
        // Check if current user is admin
        const currentUser = await sql`
            SELECT role FROM users WHERE id = ${req.userId}
        `;

        if (currentUser.length === 0 || currentUser[0].role !== 'admin') {
            return res.status(403).json({ message: "Unauthorized" });
        }

        const { page = 1, limit = 10, role, status } = req.query;
        const offset = (page - 1) * limit;

        let whereConditions = [];
        if (role) whereConditions.push(`role = '${role}'`);
        if (status) whereConditions.push(`status = '${status}'`);
        
        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const users = await sql`
            SELECT id, name, email, username, role, status, approval_status, 
                   is_email_verified, can_bid, can_participate_in_lottery, 
                   created_at, updated_at
            FROM users 
            ${sql.unsafe(whereClause)}
            ORDER BY created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM users 
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            users,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting all users:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
