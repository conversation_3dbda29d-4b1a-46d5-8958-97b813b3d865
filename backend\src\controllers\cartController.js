import { sql } from "../config/db.js";

// Get user's cart items
export async function getCartItems(req, res) {
    try {
        const userId = req.userId;
        
        const cartItems = await sql`
            SELECT c.*, p.name, p.price, p.images, p.stock, p.status
            FROM cart c
            JOIN products p ON c.product_id = p.id::text
            WHERE c.user_id = ${userId} AND p.status = 'active'
            ORDER BY c.created_at DESC
        `;

        res.status(200).json(cartItems);
    } catch (error) {
        console.error('Error getting cart items:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Add item to cart
export async function addToCart(req, res) {
    try {
        const userId = req.userId;
        const { product_id, quantity = 1 } = req.body;

        if (!product_id) {
            return res.status(400).json({ message: "Product ID is required" });
        }

        // Check if product exists and is active
        const product = await sql`
            SELECT id, stock, status FROM products 
            WHERE id = ${product_id} AND status = 'active'
        `;

        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found or inactive" });
        }

        if (product[0].stock < quantity) {
            return res.status(400).json({ message: "Insufficient stock" });
        }

        // Check if item already exists in cart
        const existingItem = await sql`
            SELECT * FROM cart 
            WHERE user_id = ${userId} AND product_id = ${product_id}
        `;

        if (existingItem.length > 0) {
            // Update quantity
            const updatedItem = await sql`
                UPDATE cart SET 
                    quantity = quantity + ${quantity},
                    updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ${userId} AND product_id = ${product_id}
                RETURNING *
            `;
            res.status(200).json(updatedItem[0]);
        } else {
            // Add new item
            const newItem = await sql`
                INSERT INTO cart (user_id, product_id, quantity)
                VALUES (${userId}, ${product_id}, ${quantity})
                RETURNING *
            `;
            res.status(201).json(newItem[0]);
        }
    } catch (error) {
        console.error('Error adding to cart:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update cart item quantity
export async function updateCartItem(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;
        const { quantity } = req.body;

        if (!quantity || quantity < 1) {
            return res.status(400).json({ message: "Valid quantity is required" });
        }

        const updatedItem = await sql`
            UPDATE cart SET 
                quantity = ${quantity},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        if (updatedItem.length === 0) {
            return res.status(404).json({ message: "Cart item not found" });
        }

        res.status(200).json(updatedItem[0]);
    } catch (error) {
        console.error('Error updating cart item:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Remove item from cart
export async function removeFromCart(req, res) {
    try {
        const userId = req.userId;
        const { id } = req.params;

        const result = await sql`
            DELETE FROM cart 
            WHERE id = ${id} AND user_id = ${userId}
            RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Cart item not found" });
        }

        res.status(200).json({ message: "Item removed from cart" });
    } catch (error) {
        console.error('Error removing from cart:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Clear user's cart
export async function clearCart(req, res) {
    try {
        const userId = req.userId;

        await sql`
            DELETE FROM cart WHERE user_id = ${userId}
        `;

        res.status(200).json({ message: "Cart cleared successfully" });
    } catch (error) {
        console.error('Error clearing cart:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get cart summary
export async function getCartSummary(req, res) {
    try {
        const userId = req.userId;

        const summary = await sql`
            SELECT 
                COUNT(*) as item_count,
                COALESCE(SUM(c.quantity * p.price), 0) as total_amount
            FROM cart c
            JOIN products p ON c.product_id = p.id::text
            WHERE c.user_id = ${userId} AND p.status = 'active'
        `;

        res.status(200).json(summary[0]);
    } catch (error) {
        console.error('Error getting cart summary:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
