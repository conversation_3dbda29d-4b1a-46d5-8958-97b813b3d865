{"version": 3, "sources": ["../src/object.ts"], "sourcesContent": ["export const without = <T extends object, P extends keyof T>(obj: T, ...props: P[]): Omit<T, P> => {\n  const copy = { ...obj };\n  for (const prop of props) {\n    delete copy[prop];\n  }\n  return copy;\n};\n\nexport const removeUndefined = <T extends object>(obj: T): Partial<T> => {\n  return Object.entries(obj).reduce((acc, [key, value]) => {\n    if (value !== undefined && value !== null) {\n      acc[key as keyof T] = value;\n    }\n    return acc;\n  }, {} as Partial<T>);\n};\n\nexport const applyFunctionToObj = <T extends Record<string, any>, R>(\n  obj: T,\n  fn: (val: any, key: string) => R,\n): Record<string, R> => {\n  const result = {} as Record<string, R>;\n  for (const key in obj) {\n    result[key] = fn(obj[key], key);\n  }\n  return result;\n};\n\nexport const filterProps = <T extends Record<string, any>>(obj: T, filter: (a: any) => boolean): T => {\n  const result = {} as T;\n  for (const key in obj) {\n    if (obj[key] && filter(obj[key])) {\n      result[key] = obj[key];\n    }\n  }\n  return result;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,UAAU,CAAsC,QAAW,UAA2B;AACjG,QAAM,OAAO,EAAE,GAAG,IAAI;AACtB,aAAW,QAAQ,OAAO;AACxB,WAAO,KAAK,IAAI;AAAA,EAClB;AACA,SAAO;AACT;AAEO,IAAM,kBAAkB,CAAmB,QAAuB;AACvE,SAAO,OAAO,QAAQ,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACvD,QAAI,UAAU,UAAa,UAAU,MAAM;AACzC,UAAI,GAAc,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAe;AACrB;AAEO,IAAM,qBAAqB,CAChC,KACA,OACsB;AACtB,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,KAAK;AACrB,WAAO,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,EAChC;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAgC,KAAQ,WAAmC;AACpG,QAAM,SAAS,CAAC;AAChB,aAAW,OAAO,KAAK;AACrB,QAAI,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,CAAC,GAAG;AAChC,aAAO,GAAG,IAAI,IAAI,GAAG;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;", "names": []}