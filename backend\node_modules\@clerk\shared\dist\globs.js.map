{"version": 3, "sources": ["../src/globs.ts"], "sourcesContent": ["import globToRegexp from 'glob-to-regexp';\n\nexport const globs = {\n  toRegexp: (pattern: string): RegExp => {\n    try {\n      return globToRegexp(pattern);\n    } catch (e: any) {\n      throw new Error(\n        `Invalid pattern: ${pattern}.\\nConsult the documentation of glob-to-regexp here: https://www.npmjs.com/package/glob-to-regexp.\\n${e.message}`,\n      );\n    }\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAAyB;AAElB,IAAM,QAAQ;AAAA,EACnB,UAAU,CAAC,YAA4B;AACrC,QAAI;AACF,iBAAO,sBAAAA,SAAa,OAAO;AAAA,IAC7B,SAAS,GAAQ;AACf,YAAM,IAAI;AAAA,QACR,oBAAoB,OAAO;AAAA;AAAA,EAAuG,EAAE,OAAO;AAAA,MAC7I;AAAA,IACF;AAAA,EACF;AACF;", "names": ["globToRegexp"]}