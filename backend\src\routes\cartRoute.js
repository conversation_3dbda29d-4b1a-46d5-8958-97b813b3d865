import express from 'express';
import {
    getCartItems,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getCartSummary
} from '../controllers/cartController.js';
import { authenticateUser } from '../middleware/auth.js';

const router = express.Router();

// All cart routes require authentication
router.get('/', authenticateUser, getCartItems);
router.get('/summary', authenticateUser, getCartSummary);
router.post('/', authenticateUser, addToCart);
router.put('/:id', authenticateUser, updateCartItem);
router.delete('/:id', authenticateUser, removeFromCart);
router.delete('/', authenticateUser, clearCart);

export default router;
