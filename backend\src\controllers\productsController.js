import { sql } from "../config/db.js";

// Get all products with pagination and filters
export async function getProducts(req, res) {
    try {
        const { 
            page = 1, 
            limit = 10, 
            category, 
            featured, 
            status = 'active',
            search,
            minPrice,
            maxPrice,
            sortBy = 'created_at',
            sortOrder = 'DESC'
        } = req.query;

        const offset = (page - 1) * limit;
        let whereConditions = [`status = ${status}`];
        let params = [];

        if (category) {
            whereConditions.push(`category = $${params.length + 1}`);
            params.push(category);
        }

        if (featured !== undefined) {
            whereConditions.push(`featured = $${params.length + 1}`);
            params.push(featured === 'true');
        }

        if (search) {
            whereConditions.push(`(name ILIKE $${params.length + 1} OR description ILIKE $${params.length + 2})`);
            params.push(`%${search}%`, `%${search}%`);
        }

        if (minPrice) {
            whereConditions.push(`price >= $${params.length + 1}`);
            params.push(parseFloat(minPrice));
        }

        if (maxPrice) {
            whereConditions.push(`price <= $${params.length + 1}`);
            params.push(parseFloat(maxPrice));
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
        
        const products = await sql`
            SELECT * FROM products 
            ${sql.unsafe(whereClause)}
            ORDER BY ${sql.unsafe(sortBy)} ${sql.unsafe(sortOrder)}
            LIMIT ${limit} OFFSET ${offset}
        `;

        const totalCount = await sql`
            SELECT COUNT(*) as count FROM products 
            ${sql.unsafe(whereClause)}
        `;

        res.status(200).json({
            products,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: parseInt(totalCount[0].count),
                pages: Math.ceil(totalCount[0].count / limit)
            }
        });
    } catch (error) {
        console.error('Error getting products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get single product by ID
export async function getProductById(req, res) {
    try {
        const { id } = req.params;
        
        const product = await sql`
            SELECT * FROM products WHERE id = ${id} AND status = 'active'
        `;

        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json(product[0]);
    } catch (error) {
        console.error('Error getting product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Create new product (requires authentication)
export async function createProduct(req, res) {
    try {
        const {
            name,
            description,
            price,
            original_price,
            discount_percentage,
            images,
            category,
            stock,
            featured = false,
            specifications = {},
            tags = [],
            dynamic_pricing = {}
        } = req.body;

        if (!name || !description || !price || !images || !category || stock === undefined) {
            return res.status(400).json({ message: "Missing required fields" });
        }

        const product = await sql`
            INSERT INTO products (
                name, description, price, original_price, discount_percentage,
                images, category, stock, featured, status, seller_id,
                specifications, tags, dynamic_pricing
            ) VALUES (
                ${name}, ${description}, ${price}, ${original_price}, ${discount_percentage},
                ${JSON.stringify(images)}, ${category}, ${stock}, ${featured}, 'active', ${req.userId},
                ${JSON.stringify(specifications)}, ${JSON.stringify(tags)}, ${JSON.stringify(dynamic_pricing)}
            ) RETURNING *
        `;

        res.status(201).json(product[0]);
    } catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Update product (requires authentication and ownership)
export async function updateProduct(req, res) {
    try {
        const { id } = req.params;
        const {
            name,
            description,
            price,
            original_price,
            discount_percentage,
            images,
            category,
            stock,
            featured,
            status,
            specifications,
            tags,
            dynamic_pricing
        } = req.body;

        // Check if product exists and user owns it
        const existingProduct = await sql`
            SELECT * FROM products WHERE id = ${id} AND seller_id = ${req.userId}
        `;

        if (existingProduct.length === 0) {
            return res.status(404).json({ message: "Product not found or unauthorized" });
        }

        const updatedProduct = await sql`
            UPDATE products SET
                name = COALESCE(${name}, name),
                description = COALESCE(${description}, description),
                price = COALESCE(${price}, price),
                original_price = COALESCE(${original_price}, original_price),
                discount_percentage = COALESCE(${discount_percentage}, discount_percentage),
                images = COALESCE(${images ? JSON.stringify(images) : null}, images),
                category = COALESCE(${category}, category),
                stock = COALESCE(${stock}, stock),
                featured = COALESCE(${featured}, featured),
                status = COALESCE(${status}, status),
                specifications = COALESCE(${specifications ? JSON.stringify(specifications) : null}, specifications),
                tags = COALESCE(${tags ? JSON.stringify(tags) : null}, tags),
                dynamic_pricing = COALESCE(${dynamic_pricing ? JSON.stringify(dynamic_pricing) : null}, dynamic_pricing),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id} AND seller_id = ${req.userId}
            RETURNING *
        `;

        res.status(200).json(updatedProduct[0]);
    } catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Delete product (requires authentication and ownership)
export async function deleteProduct(req, res) {
    try {
        const { id } = req.params;

        const result = await sql`
            DELETE FROM products 
            WHERE id = ${id} AND seller_id = ${req.userId}
            RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Product not found or unauthorized" });
        }

        res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}

// Get products by seller
export async function getProductsBySeller(req, res) {
    try {
        const { sellerId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const products = await sql`
            SELECT * FROM products 
            WHERE seller_id = ${sellerId} AND status = 'active'
            ORDER BY created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `;

        res.status(200).json(products);
    } catch (error) {
        console.error('Error getting seller products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
}
