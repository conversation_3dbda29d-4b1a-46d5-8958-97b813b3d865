/**
 * Order Entity (Domain Object)
 * İş mantığından bağımsız sipariş varlığı
 */
import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

const { OrderStatus, PaymentStatus } = require('../../shared/enums');

class Order {
  constructor({
    id = null,
    userId,
    items = [],
    totalAmount,
    shippingAddress,
    billingAddress = null,
    paymentMethod,
    paymentStatus = PaymentStatus.PENDING,
    status = OrderStatus.PENDING,
    orderNumber = null,
    notes = '',
    shippingCost = 0,
    taxAmount = 0,
    discountAmount = 0,
    couponCode = null,
    trackingNumber = null,
    estimatedDelivery = null,
    deliveredAt = null,
    cancelledAt = null,
    cancellationReason = null,
    createdAt = new Date(),
    updatedAt = new Date()
  }) {
    console.log('🔍 Order constructor - totalAmount:', totalAmount);
    this.id = id;
    this.userId = userId;
    this.items = items;
    this.totalAmount = totalAmount;
    this.shippingAddress = shippingAddress;
    this.billingAddress = billingAddress || shippingAddress;
    this.paymentMethod = paymentMethod;
    this.paymentStatus = paymentStatus;
    this.status = status;
    this.orderNumber = orderNumber || this.generateOrderNumber();
    this.notes = notes;
    this.shippingCost = shippingCost;
    this.taxAmount = taxAmount;
    this.discountAmount = discountAmount;
    this.couponCode = couponCode;
    this.trackingNumber = trackingNumber;
    this.estimatedDelivery = estimatedDelivery;
    this.deliveredAt = deliveredAt;
    this.cancelledAt = cancelledAt;
    this.cancellationReason = cancellationReason;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    console.log('🔍 Order constructor - this.totalAmount after assignment:', this.totalAmount);

    // Validation
    this.validate();
  }

  /**
   * Entity validation
   */
  validate() {
    if (!this.userId) {
      throw new Error('User ID is required');
    }

    if (!this.items || this.items.length === 0) {
      throw new Error('Order must have at least one item');
    }

    if (this.totalAmount <= 0 || this.totalAmount > 1000000) {
      throw new Error(`Total amount must be between 0 and 1000000`);
    }

    if (!this.shippingAddress) {
      throw new Error('Shipping address is required');
    }

    if (!this.paymentMethod) {
      throw new Error('Payment method is required');
    }

    if (!Object.values(OrderStatus).includes(this.status)) {
      throw new Error('Invalid order status');
    }

    if (!Object.values(PaymentStatus).includes(this.paymentStatus)) {
      throw new Error('Invalid payment status');
    }

    // Validate items
    this.items.forEach((item, index) => {
      if (!item.productId) {
        throw new Error(`Item ${index + 1}: Product ID is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        throw new Error(`Item ${index + 1}: Quantity must be greater than 0`);
      }
      if (!item.price || item.price <= 0) {
        throw new Error(`Item ${index + 1}: Price must be greater than 0`);
      }
    });

    // Validate address
    this.validateAddress(this.shippingAddress, 'Shipping address');
    if (this.billingAddress && this.billingAddress !== this.shippingAddress) {
      this.validateAddress(this.billingAddress, 'Billing address');
    }
  }

  /**
   * Address validation
   */
  validateAddress(address, addressType) {
    // Support both 'street' and 'address1' field names for flexibility
    const requiredFields = ['city', 'postalCode', 'country'];
    const hasStreetOrAddress = address.street || address.address1;

    if (!hasStreetOrAddress) {
      throw new Error(`${addressType}: Missing required field: street or address1`);
    }

    const missingFields = requiredFields.filter(field => !address[field]);

    if (missingFields.length > 0) {
      throw new Error(`${addressType}: Missing required fields: ${missingFields.join(', ')}`);
    }
  }

  /**
   * Siparişin iptal edilebilir olup olmadığını kontrol et
   */
  canBeCancelled() {
    const cancellableStatuses = [OrderStatus.PENDING, OrderStatus.CONFIRMED];
    return cancellableStatuses.includes(this.status) &&
           this.paymentStatus !== PaymentStatus.COMPLETED;
  }

  /**
   * Siparişin iade edilebilir olup olmadığını kontrol et
   */
  canBeReturned() {
    return this.status === OrderStatus.DELIVERED &&
           this.deliveredAt &&
           (new Date() - this.deliveredAt) <= (30 * 24 * 60 * 60 * 1000); // 30 days
  }

  /**
   * Sipariş durumunu güncelle
   */
  updateStatus(newStatus) {
    if (!Object.values(OrderStatus).includes(newStatus)) {
      throw new Error('Invalid order status');
    }

    // Status transition validation
    this.validateStatusTransition(this.status, newStatus);

    this.status = newStatus;
    this.updatedAt = new Date();

    // Set specific timestamps
    if (newStatus === OrderStatus.DELIVERED) {
      this.deliveredAt = new Date();
    } else if (newStatus === OrderStatus.CANCELLED) {
      this.cancelledAt = new Date();
    }
  }

  /**
   * Ödeme durumunu güncelle
   */
  updatePaymentStatus(newPaymentStatus) {
    if (!Object.values(PaymentStatus).includes(newPaymentStatus)) {
      throw new Error('Invalid payment status');
    }

    this.paymentStatus = newPaymentStatus;
    this.updatedAt = new Date();

    // Auto-confirm order when payment is completed
    if (newPaymentStatus === PaymentStatus.COMPLETED && this.status === OrderStatus.PENDING) {
      this.status = OrderStatus.CONFIRMED;
    }
  }

  /**
   * Siparişi iptal et
   */
  cancel(reason = '') {
    if (!this.canBeCancelled()) {
      throw new Error('Order cannot be cancelled');
    }

    this.status = OrderStatus.CANCELLED;
    this.cancelledAt = new Date();
    this.cancellationReason = reason;
    this.updatedAt = new Date();
  }

  /**
   * Takip numarası ekle
   */
  addTrackingNumber(trackingNumber) {
    if (!trackingNumber) {
      throw new Error('Tracking number is required');
    }

    this.trackingNumber = trackingNumber;
    this.updatedAt = new Date();

    // Auto-update status to shipped if not already
    if (this.status === OrderStatus.PROCESSING) {
      this.status = OrderStatus.SHIPPED;
    }
  }

  /**
   * Tahmini teslimat tarihi belirle
   */
  setEstimatedDelivery(date) {
    if (!(date instanceof Date)) {
      throw new Error('Estimated delivery must be a valid date');
    }

    if (date <= new Date()) {
      throw new Error('Estimated delivery must be in the future');
    }

    this.estimatedDelivery = date;
    this.updatedAt = new Date();
  }

  /**
   * Sipariş toplamını hesapla
   */
  calculateTotal() {
    const itemsTotal = this.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);

    return itemsTotal + this.shippingCost + this.taxAmount - this.discountAmount;
  }

  /**
   * Sipariş toplamını doğrula
   */
  validateTotal() {
    const calculatedTotal = this.calculateTotal();
    const difference = Math.abs(this.totalAmount - calculatedTotal);

    if (difference > 0.01) { // Allow for small rounding differences
      throw new Error(`Total amount mismatch. Expected: ${calculatedTotal}, Got: ${this.totalAmount}`);
    }
  }

  /**
   * Status transition validation
   */
  validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [], // No transitions from cancelled
      [OrderStatus.REFUNDED]: [] // No transitions from refunded
    };

    const allowedTransitions = validTransitions[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      throw new Error(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }

  /**
   * Sipariş numarası oluştur
   */
  generateOrderNumber() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `ORD-${timestamp.slice(-8)}-${random}`;
  }

  /**
   * Entity'yi plain object'e çevir
   */
  toJSON() {
    return {
      id: this.id ? this.id.toString() : null,
      _id: this.id ? this.id.toString() : null, // Frontend _id bekliyor
      userId: this.userId,
      items: this.items,
      totalAmount: this.totalAmount,
      shippingAddress: this.shippingAddress,
      billingAddress: this.billingAddress,
      paymentMethod: this.paymentMethod,
      paymentStatus: this.paymentStatus,
      status: this.status,
      orderNumber: this.orderNumber,
      notes: this.notes,
      shippingCost: this.shippingCost,
      taxAmount: this.taxAmount,
      discountAmount: this.discountAmount,
      couponCode: this.couponCode,
      trackingNumber: this.trackingNumber,
      estimatedDelivery: this.estimatedDelivery,
      deliveredAt: this.deliveredAt,
      cancelledAt: this.cancelledAt,
      cancellationReason: this.cancellationReason,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Entity'yi database için hazırla
   */
  toPersistence() {
    // Calculate subtotal from items
    const subtotal = this.items.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);

    // Map address fields for compatibility
    const mapAddress = (address) => {
      if (!address) return address;
      return {
        ...address,
        street: address.street || address.address1
      };
    };

    const data = {
      userId: this.userId,
      items: this.items,
      // Map entity fields to model fields
      subtotal: subtotal,
      total: this.totalAmount,
      shipping: this.shippingCost,
      tax: this.taxAmount,
      discount: this.discountAmount,
      // Map address fields for compatibility
      shippingAddress: mapAddress(this.shippingAddress),
      billingAddress: mapAddress(this.billingAddress),
      paymentMethod: this.paymentMethod,
      paymentStatus: this.paymentStatus,
      status: this.status,
      orderNumber: this.orderNumber,
      notes: this.notes,
      couponCode: this.couponCode,
      trackingNumber: this.trackingNumber,
      estimatedDelivery: this.estimatedDelivery,
      deliveredAt: this.deliveredAt,
      cancelledAt: this.cancelledAt,
      cancellationReason: this.cancellationReason,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };

    // Only include _id if it exists (for updates)
    if (this.id) {
      data._id = this.id;
    }

    return data;
  }

  /**
   * Database'den entity oluştur
   */
  static fromPersistence(data) {
    return new Order({
      id: data._id || data.id,
      userId: data.userId,
      items: data.items,
      // Map model fields to entity fields
      totalAmount: data.total || data.totalAmount,
      shippingCost: data.shipping || data.shippingCost || 0,
      taxAmount: data.tax || data.taxAmount || 0,
      discountAmount: data.discount || data.discountAmount || 0,
      // Keep original fields
      shippingAddress: data.shippingAddress,
      billingAddress: data.billingAddress,
      paymentMethod: data.paymentMethod,
      paymentStatus: data.paymentStatus,
      status: data.status,
      orderNumber: data.orderNumber,
      notes: data.notes,
      couponCode: data.couponCode,
      trackingNumber: data.trackingNumber,
      estimatedDelivery: data.estimatedDelivery,
      deliveredAt: data.deliveredAt,
      cancelledAt: data.cancelledAt,
      cancellationReason: data.cancellationReason,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}

module.exports = Order;
