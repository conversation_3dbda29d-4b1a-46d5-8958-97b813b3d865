import express from 'express';
import {
    getAuctions,
    getAuctionById,
    createAuction,
    placeBid,
    getAuctionBids
} from '../controllers/auctionsController.js';
import { authenticateUser, optionalAuth } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', optionalAuth, getAuctions);
router.get('/:id', getAuctionById);
router.get('/:id/bids', getAuctionBids);

// Protected routes (require authentication)
router.post('/', authenticateUser, createAuction);
router.post('/:id/bid', authenticateUser, placeBid);

export default router;
